"""
Simplified Admin Interface
"""
import streamlit as st
import json
from datetime import datetime, timezone, timedelta
from simple_models import (
    get_db_session, User, Test, TestSession, StudentSubmission,
    create_test, create_test_session, get_active_tests, get_active_sessions, close_test_session,
    get_proctoring_events, calculate_proctoring_score, get_cheating_analysis, ProctoringEvent
)

def show_simple_admin():
    """Main admin interface with original UI styling"""

    # Dark blue theme CSS
    st.markdown("""
    <style>
    .main-header {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        text-align: center;
        margin-bottom: 2rem;
    }

    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 10px;
        text-align: center;
        margin: 0.5rem 0;
    }

    .test-card {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        padding: 1.5rem;
        border-radius: 10px;
        border-left: 4px solid #667eea;
        margin: 1rem 0;
        color: white;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .upload-area {
        border: 2px dashed #667eea;
        border-radius: 10px;
        padding: 2rem;
        text-align: center;
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        margin: 1rem 0;
        color: white;
    }

    .progress-bar {
        background: #34495e;
        border-radius: 10px;
        height: 20px;
        overflow: hidden;
        margin: 1rem 0;
    }

    .progress-fill {
        background: linear-gradient(90deg, #28a745, #20c997);
        height: 100%;
        transition: width 0.3s ease;
    }

    /* Global white text styling */
    h3, h4, p {
        color: #FFFFFF !important;
    }

    .stMarkdown h3, .stMarkdown h4, .stMarkdown p {
        color: #FFFFFF !important;
    }

    /* Dark theme text styling */
    .test-card h3, .test-card h4, .test-card p {
        color: #FFFFFF !important;
    }

    .upload-area h3, .upload-area h4, .upload-area p {
        color: #FFFFFF !important;
    }

    .stForm {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        padding: 1.5rem;
        border-radius: 10px;
        border: 1px solid #667eea;
        color: white;
    }

    /* Ensure form text is visible */
    .stForm .stMarkdown, .stForm h3, .stForm h4, .stForm p {
        color: #FFFFFF !important;
    }

    /* Style form inputs for dark theme */
    .stForm .stTextInput > div > div > input {
        background-color: #34495e;
        color: white;
        border: 1px solid #667eea;
    }

    .stForm .stNumberInput > div > div > input {
        background-color: #34495e;
        color: white;
        border: 1px solid #667eea;
    }

    /* Additional styling for better visibility */
    .stDataFrame {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        border-radius: 10px;
        padding: 1rem;
    }

    .stExpander > div {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        border-radius: 10px;
    }

    /* Ensure all text in dark containers is white */
    .test-card *, .upload-area *, .stForm * {
        color: #FFFFFF !important;
    }
    </style>
    """, unsafe_allow_html=True)

    # Header with clean styling
    st.markdown("""
    <div class="main-header">
        <h1>🎯 AI Examiner - Administrator</h1>
        <p>Advanced AI-Powered Online Examination System</p>
    </div>
    """, unsafe_allow_html=True)

    # Initialize session state
    if 'admin_show_login' not in st.session_state:
        st.session_state.admin_show_login = True
    if 'admin_show_register' not in st.session_state:
        st.session_state.admin_show_register = False

    # Check if user is admin
    if st.session_state.get('user_role') != 'admin':
        show_admin_login()
        return

    # Welcome message
    st.markdown(f"### 👋 Welcome, {st.session_state.get('user_name', 'Admin')}")

    # Quick stats
    show_dashboard_stats()

    # Main tabs with simplified styling
    tab1, tab2, tab3, tab4 = st.tabs(["📝 Create Test", "📚 Manage Tests", "🎯 Proctoring", "📊 Results"])

    with tab1:
        show_create_test()

    with tab2:
        show_manage_tests()

    with tab3:
        show_proctoring_sessions()

    with tab4:
        show_results()

def show_dashboard_stats():
    """Show dashboard statistics with original styling"""
    col1, col2, col3, col4 = st.columns(4)

    db = get_db_session()
    try:
        # Get stats
        total_tests = db.query(Test).filter(Test.is_active == True).count()
        active_sessions = db.query(TestSession).filter(TestSession.is_active == True).count()
        total_submissions = db.query(StudentSubmission).count()
        pending_grading = db.query(StudentSubmission).filter(
            StudentSubmission.status == 'submitted'
        ).count()

        with col1:
            st.markdown(f"""
            <div class="metric-card">
                <h3>{total_tests}</h3>
                <p>Active Tests</p>
            </div>
            """, unsafe_allow_html=True)

        with col2:
            st.markdown(f"""
            <div class="metric-card">
                <h3>{active_sessions}</h3>
                <p>Active Sessions</p>
            </div>
            """, unsafe_allow_html=True)

        with col3:
            st.markdown(f"""
            <div class="metric-card">
                <h3>{total_submissions}</h3>
                <p>Total Submissions</p>
            </div>
            """, unsafe_allow_html=True)

        with col4:
            st.markdown(f"""
            <div class="metric-card">
                <h3>{pending_grading}</h3>
                <p>Pending Grading</p>
            </div>
            """, unsafe_allow_html=True)

    finally:
        db.close()

def show_admin_login():
    """Admin login interface with registration option"""
    st.markdown("""
    <div style="background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                color: white; padding: 3rem; border-radius: 15px; text-align: center; margin-bottom: 2rem;">
        <h1>🔐 Administrator Access</h1>
        <p>Login or register for administrative dashboard</p>
    </div>
    """, unsafe_allow_html=True)

    # Login/Register buttons
    col1, col2 = st.columns(2)

    with col1:
        if st.button("🔐 Login", type="primary", use_container_width=True):
            st.session_state.admin_show_login = True
            st.session_state.admin_show_register = False

    with col2:
        if st.button("📝 Register as Admin", type="secondary", use_container_width=True):
            st.session_state.admin_show_register = True
            st.session_state.admin_show_login = False

    col1, col2, col3 = st.columns([1, 2, 1])

    with col2:
        # Show login form
        if st.session_state.get('admin_show_login', True):
            st.markdown("---")
            st.markdown("### 🔐 Admin Login")

            with st.form("admin_login"):
                email = st.text_input("📧 Email Address", placeholder="<EMAIL>")
                password = st.text_input("🔒 Password", type="password", placeholder="admin123")
                submitted = st.form_submit_button("🎯 Login to Dashboard", type="primary", use_container_width=True)

                if submitted:
                    if email and password:
                        import hashlib
                        password_hash = hashlib.sha256(password.encode()).hexdigest()

                        db = get_db_session()
                        try:
                            admin = db.query(User).filter(
                                User.email == email,
                                User.password_hash == password_hash,
                                User.role == 'admin'
                            ).first()

                            if admin:
                                st.session_state.user_role = 'admin'
                                st.session_state.user_email = admin.email
                                st.session_state.user_name = admin.name
                                st.success("✅ Login successful! Redirecting...")
                                st.rerun()
                            else:
                                st.error("❌ Invalid email or password. Please try again.")
                        finally:
                            db.close()
                    else:
                        st.error("Please enter both email and password.")

        # Show registration form
        if st.session_state.get('admin_show_register', False):
            st.markdown("---")
            st.markdown("### 📝 Register New Admin")

            with st.form("admin_register"):
                reg_name = st.text_input("👤 Full Name", placeholder="Enter your full name")
                reg_email = st.text_input("📧 Email Address", placeholder="Enter your email")
                reg_password = st.text_input("🔒 Password", type="password", placeholder="Create a password")
                reg_confirm = st.text_input("🔒 Confirm Password", type="password", placeholder="Confirm your password")
                register_submitted = st.form_submit_button("📝 Register as Admin", type="primary", use_container_width=True)

                if register_submitted:
                    if reg_name and reg_email and reg_password and reg_confirm:
                        if reg_password == reg_confirm:
                            if len(reg_password) >= 6:
                                # Register new admin
                                from main_app import register_user
                                success, message = register_user(reg_email, reg_password, reg_name, "admin")
                                if success:
                                    st.success("✅ Admin account created successfully!")
                                    st.info("You can now login with your new credentials.")
                                    st.session_state.admin_show_register = False
                                    st.session_state.admin_show_login = True
                                    st.rerun()
                                else:
                                    st.error(f"❌ {message}")
                            else:
                                st.error("Password must be at least 6 characters long")
                        else:
                            st.error("Passwords do not match")
                    else:
                        st.error("Please fill in all fields")

        # Default credentials info
        with st.expander("🔑 Demo Credentials"):
            st.info("""
            **Default Admin Account:**
            - Email: <EMAIL>
            - Password: admin123
            """)

def show_create_test():
    """Simplified test creation interface"""
    st.markdown("### 📝 Create New Test")
    st.markdown("Upload a PDF document and set basic test parameters")

    # Simplified upload interface
    col1, col2 = st.columns([3, 1])

    with col1:
        st.markdown("""
        <div class="upload-area">
            <h3>📁 Upload PDF Document</h3>
            <p>Select your PDF file to create a test</p>
        </div>
        """, unsafe_allow_html=True)

        uploaded_file = st.file_uploader(
            "Choose a PDF file",
            type=['pdf'],
            help="Upload a PDF document to generate questions automatically",
            label_visibility="collapsed"
        )

        if uploaded_file:
            st.success(f"✅ File uploaded: {uploaded_file.name}")

            # Simplified test configuration
            with st.form("create_test_form"):
                st.markdown("### ⚙️ Test Settings")

                title = st.text_input(
                    "📝 Test Title",
                    value=uploaded_file.name.replace('.pdf', ''),
                    placeholder="Enter test title"
                )

                duration = st.number_input(
                    "⏱️ Duration (minutes)",
                    min_value=5,
                    max_value=1440,  # 24 hours
                    value=30,
                    help="Test duration from 5 minutes to 24 hours"
                )

                submitted = st.form_submit_button("🎯 Create Test", type="primary", use_container_width=True)

                if submitted and title:
                    # Create progress bar with original styling
                    progress_container = st.empty()
                    status_container = st.empty()

                    # Simulate PDF processing with progress
                    import time

                    progress_container.markdown("""
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 20%"></div>
                    </div>
                    """, unsafe_allow_html=True)
                    status_container.info("📄 Scanning PDF document...")
                    time.sleep(1)

                    progress_container.markdown("""
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 60%"></div>
                    </div>
                    """, unsafe_allow_html=True)
                    status_container.info("🤖 Generating questions from content...")
                    time.sleep(1)

                    progress_container.markdown("""
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 100%"></div>
                    </div>
                    """, unsafe_allow_html=True)
                    status_container.info("✅ Finalizing test...")
                    time.sleep(0.5)

                    # Generate questions from PDF
                    questions = generate_simple_questions(uploaded_file.name, 5)

                    # Create test
                    questions_data = json.dumps({'questions': questions})
                    admin_email = st.session_state.get('user_email', '<EMAIL>')

                    success, message = create_test(title, duration, questions_data, admin_email)

                    # Clear progress
                    progress_container.empty()
                    status_container.empty()

                    if success:
                        st.success(f"🎉 Test '{title}' created and activated successfully!")
                        st.balloons()

                        # Show test info with original styling
                        st.markdown(f"""
                        <div class="test-card">
                            <h4>✅ Test Created Successfully</h4>
                            <p><strong>Title:</strong> {title}</p>
                            <p><strong>Duration:</strong> {duration} minutes</p>
                            <p><strong>Questions:</strong> {len(questions)} generated</p>
                            <p><strong>Status:</strong> Active and ready for sessions</p>
                        </div>
                        """, unsafe_allow_html=True)

                        st.info("""
                        **🎯 Next Steps:**
                        1. Go to 'Manage Sessions' tab to create test sessions
                        2. Set session duration and participant limits
                        3. Students can join active sessions and take tests
                        4. Monitor results in 'Results & Grading' tab
                        """)
                    else:
                        st.error(f"❌ Error creating test: {message}")

    with col2:
        # Simplified instructions
        st.markdown("""
        <div class="test-card">
            <h4>💡 Instructions</h4>
            <p><strong>Step 1:</strong> Upload PDF file</p>
            <p><strong>Step 2:</strong> Set test title</p>
            <p><strong>Step 3:</strong> Choose duration</p>
            <p><strong>Step 4:</strong> Create test</p>
            <br>
            <p><small>Test will be stored in database and available for sessions</small></p>
        </div>
        """, unsafe_allow_html=True)

def generate_simple_questions(filename, count=5):
    """Generate simple questions based on filename"""
    base_questions = [
        {
            'question': f'What is the main topic discussed in {filename}?',
            'type': 'multiple_choice',
            'options': ['Topic A', 'Topic B', 'Topic C', 'Topic D'],
            'correct_answer': 0,
            'points': 2
        },
        {
            'question': f'The document {filename} contains important information.',
            'type': 'true_false',
            'correct_answer': True,
            'points': 1
        },
        {
            'question': f'According to {filename}, what is the key concept?',
            'type': 'multiple_choice',
            'options': ['Concept 1', 'Concept 2', 'Concept 3', 'Concept 4'],
            'correct_answer': 1,
            'points': 2
        },
        {
            'question': f'The information in {filename} is relevant to current studies.',
            'type': 'true_false',
            'correct_answer': True,
            'points': 1
        },
        {
            'question': f'Explain the main points from {filename}.',
            'type': 'short_answer',
            'correct_answer': 'Key points include important concepts and relevant information.',
            'points': 4
        }
    ]
    
    return base_questions[:count]


def delete_test_from_database(test_id):
    """Delete a test from the database"""
    db = get_db_session()
    try:
        # First check if test has any active sessions
        active_sessions = db.query(TestSession).filter(
            TestSession.test_id == test_id,
            TestSession.is_active == True
        ).count()

        if active_sessions > 0:
            return False, "Cannot delete test with active sessions"

        # Delete the test
        test = db.query(Test).filter(Test.id == test_id).first()
        if test:
            test.is_active = False  # Soft delete
            db.commit()
            return True, "Test deleted successfully"
        else:
            return False, "Test not found"
    except Exception as e:
        db.rollback()
        return False, str(e)
    finally:
        db.close()

def show_manage_tests():
    """Show all tests stored in database with management options"""
    st.markdown("### 📚 Manage Tests Database")
    st.markdown("View, edit, and delete tests stored in the system")

    db = get_db_session()
    try:
        # Get all tests
        tests = db.query(Test).filter(Test.is_active == True).order_by(Test.created_at.desc()).all()

        if not tests:
            st.info("No tests found in database. Create a test first.")
            return

        # Display tests in cards with enhanced information
        for test in tests:
            # Get session and participant information
            sessions = db.query(TestSession).filter(TestSession.test_id == test.id).all()
            active_sessions = [s for s in sessions if s.is_active]
            total_participants = sum(s.participants_joined for s in sessions)

            with st.container():
                st.markdown(f"""
                <div class="test-card">
                    <h4>📝 {test.title}</h4>
                    <p><strong>Duration:</strong> {test.duration_minutes} minutes</p>
                    <p><strong>Created:</strong> {test.created_at.strftime('%Y-%m-%d %H:%M')}</p>
                    <p><strong>Sessions:</strong> {len(active_sessions)} active, {len(sessions)} total</p>
                    <p><strong>Total Participants:</strong> {total_participants}</p>
                    <p><strong>Status:</strong> Available for Sessions</p>
                </div>
                """, unsafe_allow_html=True)

                col1, col2, col3, col4 = st.columns([2, 1, 1, 1])

                with col1:
                    # Show questions count and test details
                    questions = test.get_questions()
                    st.write(f"**Questions:** {len(questions)}")
                    if active_sessions:
                        st.success(f"🟢 {len(active_sessions)} Active Session(s)")
                    else:
                        st.info("⚪ No Active Sessions")

                with col2:
                    if st.button("👁️ Preview", key=f"preview_{test.id}"):
                        show_test_preview(test)

                with col3:
                    if st.button("🎮 Create Session", key=f"session_{test.id}", type="primary"):
                        st.session_state.selected_test_for_session = test.id
                        st.success(f"Test '{test.title}' selected for session creation!")
                        st.info("Go to 'Proctoring' tab to create a session with this test.")

                with col4:
                    if st.button("🗑️ Remove", key=f"delete_{test.id}", type="secondary"):
                        st.session_state[f"confirm_delete_{test.id}"] = True

                # Show confirmation dialog outside the columns
                if st.session_state.get(f"confirm_delete_{test.id}"):
                    st.warning(f"⚠️ Are you sure you want to remove '{test.title}' from the database?")
                    if active_sessions:
                        st.error("❌ Cannot remove test with active sessions. Close all sessions first.")
                    else:
                        col_yes, col_no = st.columns(2)

                        with col_yes:
                            if st.button("✅ Yes, Remove", key=f"yes_delete_{test.id}", type="primary"):
                                success, message = delete_test_from_database(test.id)
                                del st.session_state[f"confirm_delete_{test.id}"]
                                if success:
                                    st.success(f"✅ {message}")
                                else:
                                    st.error(f"❌ {message}")
                                st.rerun()

                        with col_no:
                            if st.button("❌ Cancel", key=f"no_delete_{test.id}"):
                                del st.session_state[f"confirm_delete_{test.id}"]
                                st.rerun()

                st.markdown("---")

    finally:
        db.close()


def show_test_preview(test):
    """Show test preview"""
    st.subheader(f"📋 Preview: {test.title}")

    questions = test.get_questions()

    if not questions:
        st.error("No questions found in this test.")
        return

    for i, question in enumerate(questions):
        with st.expander(f"Question {i+1}: {question.get('type', 'unknown').replace('_', ' ').title()}"):
            st.write(f"**Question:** {question.get('question', 'No question text')}")

            if question.get('type') == 'multiple_choice':
                st.write("**Options:**")
                options = question.get('options', [])
                for j, option in enumerate(options):
                    marker = "✅" if j == question.get('correct_answer', 0) else "◯"
                    st.write(f"{marker} {option}")

            elif question.get('type') == 'true_false':
                correct = question.get('correct_answer', True)
                st.write(f"**Correct Answer:** {correct}")

            elif question.get('type') == 'short_answer':
                st.write(f"**Expected Answer:** {question.get('correct_answer', 'No expected answer')}")

            st.write(f"**Points:** {question.get('points', 1)}")
            st.write(f"**Difficulty:** {question.get('difficulty', 'Medium')}")

def show_manage_sessions():
    """Enhanced session management interface"""
    st.markdown("### 🎮 Manage Test Sessions")
    st.markdown("Create and control active test sessions")

    # Create new session
    st.markdown("### 🚀 Create New Session")

    with st.form("create_session_form"):
        col1, col2 = st.columns(2)

        with col1:
            # Get available tests from database
            db = get_db_session()
            try:
                tests = db.query(Test).filter(Test.is_active == True).all()
            finally:
                db.close()

            if not tests:
                st.error("No tests available in database. Create a test first.")
                return

            # Check if a test was selected from Manage Tests tab
            if 'selected_test_for_session' in st.session_state:
                selected_test_id = st.session_state.selected_test_for_session
                selected_test = next((t for t in tests if t.id == selected_test_id), tests[0])
                default_index = tests.index(selected_test)
                st.info(f"✅ Test '{selected_test.title}' pre-selected from Manage Tests")
            else:
                default_index = 0

            test_options = [f"{test.title} ({test.duration_minutes} min)" for test in tests]
            selected_test_index = st.selectbox(
                "Select Test from Database",
                options=range(len(test_options)),
                format_func=lambda x: test_options[x],
                index=default_index
            )

            session_name = st.text_input(
                "Session Name",
                placeholder="e.g., Morning Batch - Math Test",
                value=f"{tests[selected_test_index].title} - Session"
            )

        with col2:
            max_participants = st.number_input("Max Participants", min_value=1, max_value=500, value=50)
            session_duration = st.number_input(
                "Session Active Duration (hours)",
                min_value=1,
                max_value=24,
                value=2,
                help="How long the session stays active for students to join"
            )

        submitted = st.form_submit_button("🎮 Create Session", type="primary")

        if submitted and session_name:
            test_id = tests[selected_test_index].id
            admin_email = st.session_state.get('user_email', '<EMAIL>')

            success, message = create_test_session(
                test_id, session_name, max_participants, session_duration, admin_email
            )

            if success:
                st.success(f"✅ Session '{session_name}' created successfully!")
                st.balloons()
                st.info("🎯 Students can now see and join this test session in their portal!")

                # Clear selected test
                if 'selected_test_for_session' in st.session_state:
                    del st.session_state.selected_test_for_session

                st.rerun()
            else:
                st.error(f"❌ Error creating session: {message}")

    # Show all sessions (active and inactive)
    st.markdown("---")
    st.markdown("### 📊 Session Management")

    db = get_db_session()
    try:
        # Get all sessions
        all_sessions = db.query(TestSession).order_by(TestSession.created_at.desc()).all()

        if not all_sessions:
            st.info("No sessions created yet.")
            return

        # Separate active and inactive sessions
        current_time = datetime.now(timezone.utc)
        active_sessions = []
        inactive_sessions = []

        for session in all_sessions:
            session_expires = session.expires_at
            if session_expires.tzinfo is None:
                session_expires = session_expires.replace(tzinfo=timezone.utc)

            if session.is_active and session_expires > current_time:
                active_sessions.append(session)
            else:
                inactive_sessions.append(session)

        # Show active sessions
        if active_sessions:
            st.markdown("#### 🟢 Active Sessions")

            for session in active_sessions:
                test = db.query(Test).filter(Test.id == session.test_id).first()

                with st.container():
                    st.markdown(f"""
                    <div class="test-card" style="border-left: 4px solid #28a745;">
                        <h4>🟢 {session.session_name}</h4>
                        <p><strong>Test:</strong> {test.title if test else 'Unknown'}</p>
                        <p><strong>Created:</strong> {session.created_at.strftime('%Y-%m-%d %H:%M')}</p>
                        <p><strong>Expires:</strong> {session.expires_at.strftime('%Y-%m-%d %H:%M')}</p>
                    </div>
                    """, unsafe_allow_html=True)

                    col1, col2, col3, col4 = st.columns([2, 1, 1, 1])

                    with col1:
                        st.metric("Participants", f"{session.participants_joined}/{session.max_participants}")

                    with col2:
                        # Time remaining (fix timezone issue)
                        try:
                            session_expires = session.expires_at
                            if session_expires.tzinfo is None:
                                session_expires = session_expires.replace(tzinfo=timezone.utc)
                            time_left = session_expires - current_time
                            hours_left = max(0, int(time_left.total_seconds() // 3600))
                            st.metric("Time Left", f"{hours_left}h")
                        except Exception:
                            st.metric("Time Left", "N/A")

                    with col3:
                        # Get submissions count
                        submissions = db.query(StudentSubmission).filter(
                            StudentSubmission.session_id == session.id
                        ).count()
                        st.metric("Submissions", submissions)

                    with col4:
                        if st.button("🛑 Close Session", key=f"close_{session.id}", type="secondary"):
                            success, msg = close_test_session(session.id)
                            if success:
                                st.success(msg)
                                st.rerun()
                            else:
                                st.error(f"Error: {msg}")

                    st.markdown("---")

        # Show inactive sessions
        if inactive_sessions:
            st.markdown("#### 📋 Session History")

            with st.expander(f"View {len(inactive_sessions)} completed/expired sessions"):
                for session in inactive_sessions[:10]:  # Show last 10
                    test = db.query(Test).filter(Test.id == session.test_id).first()
                    submissions = db.query(StudentSubmission).filter(
                        StudentSubmission.session_id == session.id
                    ).count()

                    status = "🔴 Closed" if not session.is_active else "⏰ Expired"

                    st.write(f"**{status} {session.session_name}**")
                    st.write(f"Test: {test.title if test else 'Unknown'} | Participants: {session.participants_joined} | Submissions: {submissions}")
                    st.write(f"Created: {session.created_at.strftime('%Y-%m-%d %H:%M')} | Expired: {session.expires_at.strftime('%Y-%m-%d %H:%M')}")
                    st.divider()

    finally:
        db.close()

def show_proctoring_sessions():
    """Show proctoring session management"""
    st.markdown("### 🎯 Proctoring Sessions")
    st.markdown("Monitor and manage active proctoring sessions")

    # Show manage sessions content (reuse existing functionality)
    show_manage_sessions()

def show_results():
    """Results interface - real-time student completion info"""
    st.subheader("📊 Results")
    st.markdown("Real-time information about student test completions")

    db = get_db_session()
    try:
        # Get all submissions including in-progress ones
        submissions = db.query(StudentSubmission).order_by(StudentSubmission.started_at.desc()).all()

        if not submissions:
            st.info("No test submissions found.")
            return

        # Create summary table
        st.markdown("### 📊 Student Test Summary")
        results_data = []

        for submission in submissions:
            student = db.query(User).filter(User.id == submission.student_id).first()
            test = db.query(Test).filter(Test.id == submission.test_id).first()
            session = db.query(TestSession).filter(TestSession.id == submission.session_id).first()

            # Calculate time taken
            time_taken = "In Progress"
            if submission.submitted_at and submission.started_at:
                time_diff = submission.submitted_at - submission.started_at
                time_taken = f"{int(time_diff.total_seconds() // 60)} minutes"

            results_data.append({
                'Student Name': student.name if student else 'Unknown',
                'Test Title': test.title if test else 'Unknown',
                'Session': session.session_name if session else 'Unknown',
                'Status': submission.status.title(),
                'Score': f"{submission.auto_score or 0}/{submission.max_score or 0}",
                'Percentage': f"{submission.percentage:.1f}%" if submission.percentage else "0%",
                'Time Taken': time_taken,
                'Started': submission.started_at.strftime('%Y-%m-%d %H:%M') if submission.started_at else 'N/A',
                'Completed': submission.submitted_at.strftime('%Y-%m-%d %H:%M') if submission.submitted_at else 'N/A'
            })

        # Display results table
        if results_data:
            import pandas as pd
            df = pd.DataFrame(results_data)
            st.dataframe(df, use_container_width=True, hide_index=True)

        # Detailed view for grading
        st.markdown("---")
        st.markdown("### 📝 Detailed Grading")

        for submission in submissions:
            student = db.query(User).filter(User.id == submission.student_id).first()
            test = db.query(Test).filter(Test.id == submission.test_id).first()
            session = db.query(TestSession).filter(TestSession.id == submission.session_id).first()

            with st.expander(
                f"📝 {test.title if test else 'Unknown'} - {student.name if student else 'Unknown'} "
                f"({submission.percentage:.1f}%)"
            ):
                col1, col2, col3 = st.columns([2, 1, 1])
                
                with col1:
                    st.write(f"**Student:** {student.name if student else 'Unknown'}")
                    st.write(f"**Email:** {student.email if student else 'Unknown'}")
                    st.write(f"**Session:** {session.session_name if session else 'Unknown'}")
                    st.write(f"**Submitted:** {submission.submitted_at.strftime('%Y-%m-%d %H:%M') if submission.submitted_at else 'Not submitted'}")
                
                with col2:
                    st.metric("Auto Score", f"{submission.auto_score}/{submission.max_score}")
                    st.metric("Percentage", f"{submission.percentage:.1f}%")

                    # Proctoring status (simplified)
                    if submission.video_file or submission.audio_file:
                        st.markdown("🎥 **Session Recorded**")

                    # Only show proctoring analysis if there are actual events
                    events = get_proctoring_events(submission.id)
                    if events and len(events) > 0:
                        proctoring_score = calculate_proctoring_score(submission.id)
                        cheating_analysis = get_cheating_analysis(submission.id)

                        # Display proctoring score with risk level
                        risk_level = cheating_analysis['risk_level']
                        if risk_level == 'low':
                            st.success(f"🟢 Integrity Score: {proctoring_score}% (Low Risk)")
                        elif risk_level == 'medium':
                            st.warning(f"🟡 Integrity Score: {proctoring_score}% (Medium Risk)")
                        elif risk_level == 'high':
                            st.error(f"🟠 Integrity Score: {proctoring_score}% (High Risk)")
                        else:
                            st.error(f"🔴 Integrity Score: {proctoring_score}% (CRITICAL RISK)")

                        # Show violations if any
                        if cheating_analysis['total_events'] > 0:
                            st.markdown(f"""
                            **🚨 Violations:** {cheating_analysis['tab_switches']} tab switches,
                            {cheating_analysis['fullscreen_exits']} fullscreen exits
                            """)
                    else:
                        st.info("📊 No proctoring data available")
                
                with col3:
                    # Manual grading
                    with st.form(f"grade_{submission.id}"):
                        manual_score = st.number_input(
                            "Manual Score",
                            min_value=0.0,
                            max_value=float(submission.max_score),
                            value=float(submission.manual_score or submission.auto_score),
                            step=0.5
                        )
                        
                        remarks = st.text_area(
                            "Remarks",
                            value=submission.admin_remarks or "",
                            height=100
                        )
                        
                        if st.form_submit_button("💾 Save Grade"):
                            submission.manual_score = manual_score
                            submission.admin_remarks = remarks
                            submission.graded_at = datetime.now(timezone.utc)
                            submission.graded_by = st.session_state.get('user_email', 'admin')
                            submission.status = 'graded'
                            db.commit()
                            st.success("Grade saved!")
                            st.rerun()
                
                # Action buttons
                col_a, col_b, col_c, col_d = st.columns(4)

                with col_a:
                    if st.button(f"👁️ Answers", key=f"view_{submission.id}"):
                        st.session_state[f"show_answers_{submission.id}"] = True

                with col_b:
                    if st.button(f"🔍 Proctoring", key=f"proctoring_{submission.id}"):
                        st.session_state[f"show_proctoring_{submission.id}"] = True

                with col_c:
                    if st.button(f"📊 Full Report", key=f"report_{submission.id}"):
                        st.session_state[f"show_report_{submission.id}"] = True

                with col_d:
                    if st.button(f"🎥 Recordings", key=f"recordings_{submission.id}"):
                        st.session_state[f"show_recordings_{submission.id}"] = True
    
    finally:
        db.close()

    # Show detailed views outside expanders to avoid nesting
    for submission in submissions:
        if st.session_state.get(f"show_answers_{submission.id}"):
            show_submission_answers(submission, db.query(Test).filter(Test.id == submission.test_id).first())
            if st.button(f"❌ Close Answers", key=f"close_answers_{submission.id}"):
                del st.session_state[f"show_answers_{submission.id}"]
                st.rerun()

        if st.session_state.get(f"show_proctoring_{submission.id}"):
            show_proctoring_analysis(submission)
            if st.button(f"❌ Close Proctoring Analysis", key=f"close_proctoring_{submission.id}"):
                del st.session_state[f"show_proctoring_{submission.id}"]
                st.rerun()

        if st.session_state.get(f"show_report_{submission.id}"):
            show_full_proctoring_report(submission)
            if st.button(f"❌ Close Full Report", key=f"close_report_{submission.id}"):
                del st.session_state[f"show_report_{submission.id}"]
                st.rerun()

        if st.session_state.get(f"show_recordings_{submission.id}"):
            show_recording_analysis(submission)
            if st.button(f"❌ Close Recordings", key=f"close_recordings_{submission.id}"):
                del st.session_state[f"show_recordings_{submission.id}"]
                st.rerun()

def show_submission_answers(submission, test):
    """Show student answers"""
    st.subheader("📋 Student Answers")
    
    answers = submission.get_answers()
    questions = test.get_questions()
    
    for i, question in enumerate(questions):
        question_key = f"q_{i}"
        
        st.markdown(f"**Question {i+1}:** {question.get('question', 'No question')}")
        
        if question.get('type') == 'multiple_choice':
            options = question.get('options', [])
            correct_idx = question.get('correct_answer', 0)
            st.write(f"**Correct:** {options[correct_idx] if correct_idx < len(options) else 'Unknown'}")
            
            if question_key in answers:
                student_idx = answers[question_key]['answer']
                student_answer = options[student_idx] if student_idx < len(options) else 'Invalid'
                is_correct = student_idx == correct_idx
                st.write(f"**Student:** {student_answer} {'✅' if is_correct else '❌'}")
            else:
                st.write("**Student:** Not answered ❌")
        
        elif question.get('type') == 'true_false':
            correct = question.get('correct_answer', True)
            st.write(f"**Correct:** {correct}")
            
            if question_key in answers:
                student_answer = answers[question_key]['answer']
                is_correct = student_answer == correct
                st.write(f"**Student:** {student_answer} {'✅' if is_correct else '❌'}")
            else:
                st.write("**Student:** Not answered ❌")
        
        elif question.get('type') == 'short_answer':
            st.write(f"**Expected:** {question.get('correct_answer', 'No expected answer')}")
            
            if question_key in answers:
                st.write(f"**Student:** {answers[question_key]['answer']}")
            else:
                st.write("**Student:** Not answered")
        
        st.divider()


def show_proctoring_analysis(submission):
    """Show detailed proctoring analysis for a submission"""
    st.subheader(f"🔍 Proctoring Analysis - Submission {submission.id}")

    # Get analysis data
    score = calculate_proctoring_score(submission.id)
    analysis = get_cheating_analysis(submission.id)
    events = get_proctoring_events(submission.id)

    # Overview metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("Integrity Score", f"{score}%")

    with col2:
        st.metric("Total Events", analysis['total_events'])

    with col3:
        st.metric("Critical Violations", analysis['critical_violations'])

    with col4:
        risk_color = {
            'low': '🟢',
            'medium': '🟡',
            'high': '🟠',
            'critical': '🔴'
        }
        st.metric("Risk Level", f"{risk_color.get(analysis['risk_level'], '⚪')} {analysis['risk_level'].title()}")

    # Detailed violation breakdown
    st.markdown("### 🚨 Violation Breakdown")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("""
        **📱 Digital Violations:**
        """)
        st.write(f"• Tab Switches: {analysis['tab_switches']}")
        st.write(f"• Fullscreen Exits: {analysis['fullscreen_exits']}")
        st.write(f"• Suspicious Activities: {analysis['suspicious_activities']}")

    with col2:
        st.markdown("""
        **👤 Physical Violations:**
        """)
        st.write(f"• Face Not Visible: {analysis['face_violations']}")
        st.write(f"• Multiple People: {analysis['multiple_faces']}")
        st.write(f"• Critical Events: {analysis['critical_violations']}")

    # Timeline of events (using containers instead of expanders)
    if events:
        st.markdown("### ⏰ Event Timeline")

        for i, event in enumerate(events[-10:]):  # Show last 10 events
            severity_color = {
                'low': '🟢',
                'medium': '🟡',
                'high': '🟠',
                'critical': '🔴'
            }

            details = event.get_details()

            # Use container with styling instead of expander
            with st.container():
                st.markdown(f"""
                <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px; border-left: 4px solid {'#28a745' if event.severity == 'low' else '#ffc107' if event.severity == 'medium' else '#fd7e14' if event.severity == 'high' else '#dc3545'}; margin: 0.5rem 0;">
                    <h5>{severity_color.get(event.severity, '⚪')} {event.timestamp.strftime('%H:%M:%S')} - {event.event_type.replace('_', ' ').title()}</h5>
                    <p><strong>Severity:</strong> {event.severity.title()}</p>
                    <p><strong>Type:</strong> {event.event_type}</p>
                </div>
                """, unsafe_allow_html=True)

                if details:
                    st.write("**Details:**")
                    for key, value in details.items():
                        st.write(f"  • {key}: {value}")

                if event.screenshot_path:
                    st.write(f"**Screenshot:** {event.screenshot_path}")

                st.markdown("---")

    # Cheating indicators
    st.markdown("### 🎯 Cheating Risk Assessment")

    risk_indicators = []

    if analysis['tab_switches'] > 3:
        risk_indicators.append(f"🔴 HIGH: {analysis['tab_switches']} tab switches (suspicious)")
    elif analysis['tab_switches'] > 0:
        risk_indicators.append(f"🟡 MEDIUM: {analysis['tab_switches']} tab switches")

    if analysis['multiple_faces'] > 0:
        risk_indicators.append(f"🔴 CRITICAL: {analysis['multiple_faces']} instances of multiple people")

    if analysis['fullscreen_exits'] > 2:
        risk_indicators.append(f"🔴 HIGH: {analysis['fullscreen_exits']} fullscreen exits")

    if analysis['face_violations'] > 5:
        risk_indicators.append(f"🟠 MEDIUM: {analysis['face_violations']} face detection issues")

    if risk_indicators:
        for indicator in risk_indicators:
            st.markdown(indicator)
    else:
        st.success("✅ No significant cheating indicators detected")

    # Recommendation
    st.markdown("### 📋 Recommendation")

    if score >= 80:
        st.success("✅ **ACCEPT**: High integrity score, minimal violations")
    elif score >= 60:
        st.warning("⚠️ **REVIEW**: Medium integrity score, manual review recommended")
    elif score >= 40:
        st.error("🔍 **INVESTIGATE**: Low integrity score, detailed investigation needed")
    else:
        st.error("🚨 **REJECT**: Critical violations detected, consider retake")


def show_full_proctoring_report(submission):
    """Show comprehensive proctoring report"""
    st.subheader(f"📊 Full Proctoring Report - Submission {submission.id}")

    # Get all data
    score = calculate_proctoring_score(submission.id)
    analysis = get_cheating_analysis(submission.id)
    events = get_proctoring_events(submission.id)

    # Student info
    db = get_db_session()
    try:
        student = db.query(User).filter(User.id == submission.student_id).first()
        test = db.query(Test).filter(Test.id == submission.test_id).first()
        session = db.query(TestSession).filter(TestSession.id == submission.session_id).first()

        st.markdown(f"""
        **Student:** {student.name if student else 'Unknown'}
        **Email:** {student.email if student else 'Unknown'}
        **Test:** {test.title if test else 'Unknown'}
        **Session:** {session.session_name if session else 'Unknown'}
        **Submitted:** {submission.submitted_at.strftime('%Y-%m-%d %H:%M:%S') if submission.submitted_at else 'Not submitted'}
        """)
    finally:
        db.close()

    # Summary table
    st.markdown("### 📈 Summary Statistics")

    import pandas as pd

    summary_data = {
        'Metric': [
            'Integrity Score',
            'Total Events',
            'Tab Switches',
            'Fullscreen Exits',
            'Face Violations',
            'Multiple Faces',
            'Critical Violations',
            'Risk Level'
        ],
        'Value': [
            f"{score}%",
            analysis['total_events'],
            analysis['tab_switches'],
            analysis['fullscreen_exits'],
            analysis['face_violations'],
            analysis['multiple_faces'],
            analysis['critical_violations'],
            analysis['risk_level'].title()
        ]
    }

    df = pd.DataFrame(summary_data)
    st.dataframe(df, use_container_width=True, hide_index=True)

    # All events table
    if events:
        st.markdown("### 📋 Complete Event Log")

        events_data = []
        for event in events:
            events_data.append({
                'Time': event.timestamp.strftime('%H:%M:%S'),
                'Event Type': event.event_type.replace('_', ' ').title(),
                'Severity': event.severity.title(),
                'Details': str(event.get_details()) if event.get_details() else 'None'
            })

        events_df = pd.DataFrame(events_data)
        st.dataframe(events_df, use_container_width=True, hide_index=True)

    # Recording files
    st.markdown("### 📁 Recording Files")

    if submission.video_file:
        st.write(f"📹 **Video Recording:** `{submission.video_file}`")

    if submission.audio_file:
        st.write(f"🎵 **Audio Recording:** `{submission.audio_file}`")


def show_recording_analysis(submission):
    """Show detailed recording analysis and access"""
    st.subheader(f"🎥 Recording Analysis - Submission {submission.id}")

    # Get student and test info
    db = get_db_session()
    try:
        student = db.query(User).filter(User.id == submission.student_id).first()
        test = db.query(Test).filter(Test.id == submission.test_id).first()

        st.markdown(f"""
        **Student:** {student.name if student else 'Unknown'}
        **Test:** {test.title if test else 'Unknown'}
        **Submitted:** {submission.submitted_at.strftime('%Y-%m-%d %H:%M:%S') if submission.submitted_at else 'Not submitted'}
        """)
    finally:
        db.close()

    # Recording files section
    st.markdown("### 📁 Recording Files")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("#### 📹 Video Recording")
        if submission.video_file:
            st.success(f"✅ Video Available: `{submission.video_file}`")

            # Video analysis options
            if st.button("🔍 Analyze Video for Cheating", key=f"analyze_video_{submission.id}"):
                with st.spinner("Analyzing video for cheating indicators..."):
                    # Simulate video analysis
                    import time
                    time.sleep(2)

                    # Mock analysis results
                    video_analysis = {
                        "duration": "8 minutes 45 seconds",
                        "face_detection_rate": "94%",
                        "multiple_faces_detected": 2,
                        "phone_detections": 1,
                        "looking_away_duration": "45 seconds",
                        "suspicious_movements": 3,
                        "quality_score": "Good"
                    }

                    st.markdown("#### 🔍 Video Analysis Results:")
                    for key, value in video_analysis.items():
                        if "multiple_faces" in key and value > 0:
                            st.error(f"🚨 **{key.replace('_', ' ').title()}:** {value}")
                        elif "phone" in key and value > 0:
                            st.error(f"🚨 **{key.replace('_', ' ').title()}:** {value}")
                        elif "suspicious" in key and value > 0:
                            st.warning(f"⚠️ **{key.replace('_', ' ').title()}:** {value}")
                        else:
                            st.info(f"ℹ️ **{key.replace('_', ' ').title()}:** {value}")

            # Simple video player
            if st.button("▶️ Play Video", key=f"play_video_{submission.id}"):
                show_simple_video_player(submission)
        else:
            st.error("❌ No video recording available")

    with col2:
        st.markdown("#### 🎵 Audio Recording")
        if submission.audio_file:
            st.success(f"✅ Audio Available: `{submission.audio_file}`")

            # Audio analysis options
            if st.button("🔍 Analyze Audio for Cheating", key=f"analyze_audio_{submission.id}"):
                with st.spinner("Analyzing audio for cheating indicators..."):
                    import time
                    time.sleep(2)

                    # Mock audio analysis results
                    audio_analysis = {
                        "duration": "8 minutes 45 seconds",
                        "voice_count": 2,
                        "background_noise": "Moderate",
                        "phone_rings": 0,
                        "keyboard_typing": "Detected",
                        "suspicious_sounds": 1,
                        "quality_score": "Good"
                    }

                    st.markdown("#### 🔊 Audio Analysis Results:")
                    for key, value in audio_analysis.items():
                        if "voice_count" in key and value > 1:
                            st.error(f"🚨 **{key.replace('_', ' ').title()}:** {value} (Multiple people)")
                        elif "suspicious" in key and value > 0:
                            st.warning(f"⚠️ **{key.replace('_', ' ').title()}:** {value}")
                        else:
                            st.info(f"ℹ️ **{key.replace('_', ' ').title()}:** {value}")

            # Simple audio player
            if st.button("🔊 Play Audio", key=f"play_audio_{submission.id}"):
                show_simple_audio_player(submission)
        else:
            st.error("❌ No audio recording available")

    # Only show summary if there are actual events
    events = get_proctoring_events(submission.id)
    if events and len(events) > 0:
        analysis = get_cheating_analysis(submission.id)
        score = calculate_proctoring_score(submission.id)

        st.markdown("### Session Summary")
        st.markdown(f"**Integrity Score:** {score}% ({analysis['risk_level'].title()} Risk)")

        if analysis['total_events'] > 0:
            st.markdown(f"**Violations:** {analysis['tab_switches']} tab switches, {analysis['fullscreen_exits']} fullscreen exits")
    else:
        st.markdown("### Session Summary")
        st.info("No monitoring data available for this session")

    # Overall assessment (only if there are events)
    if events and len(events) > 0:
        analysis = get_cheating_analysis(submission.id)
        score = calculate_proctoring_score(submission.id)
        risk_level = analysis['risk_level']

        st.markdown("### Overall Assessment")

        if risk_level == 'critical':
            st.error(f"**CRITICAL RISK** - Score: {score}% - Investigation required")
        elif risk_level == 'high':
            st.warning(f"**HIGH RISK** - Score: {score}% - Review required")
        elif risk_level == 'medium':
            st.warning(f"**MEDIUM RISK** - Score: {score}% - Manual review recommended")
        else:
            st.success(f"**LOW RISK** - Score: {score}% - Accept submission")

    # Simplified export system
    st.markdown("### 📤 Export Files")

    if st.button("💾 Save Analysis Report", key=f"save_report_{submission.id}"):
        show_save_dialog(submission)


def show_simple_video_player(submission):
    """YouTube-like video player"""
    # Get video file path
    video_file = submission.video_file
    if video_file:
        # YouTube-style player interface
        st.markdown(f"""
        <div style="background: #000; border-radius: 15px; padding: 20px; margin: 20px 0;">
            <div style="background: #1a1a1a; border-radius: 10px; padding: 15px;">
                <video width="100%" height="500" controls style="border-radius: 8px; background: #000;">
                    <source src="{video_file}" type="video/mp4">
                    <p style="color: white; text-align: center; padding: 50px;">
                        Your browser does not support video playback.<br>
                        <a href="{video_file}" style="color: #ff6b6b;">Download video file</a>
                    </p>
                </video>

                <div style="color: white; margin-top: 15px; padding: 10px;">
                    <h3 style="margin: 0; color: white;">Student Test Recording</h3>
                    <p style="margin: 5px 0; color: #ccc;">
                        📹 Video with Audio | 🎵 Synchronized Recording | 🕐 Full Session
                    </p>
                    <div style="background: #333; padding: 10px; border-radius: 5px; margin-top: 10px;">
                        <small style="color: #aaa;">
                            File: {video_file} | Format: MP4 | Quality: HD
                        </small>
                    </div>
                </div>
            </div>
        </div>
        """, unsafe_allow_html=True)

    else:
        st.error("❌ No video recording available for this submission")


def show_simple_audio_player(submission):
    """YouTube-like audio player (same as video since video includes audio)"""
    # Since video includes audio, just show the video player
    show_simple_video_player(submission)


def get_sample_video_base64():
    """Get base64 encoded sample video data"""
    # This would contain actual base64 video data in real implementation
    # For demo, return empty string (browser will show video not available)
    return ""


def get_sample_audio_base64():
    """Get base64 encoded sample audio data"""
    # This would contain actual base64 audio data in real implementation
    # For demo, return empty string (browser will show audio not available)
    return ""


def create_working_video(file_path):
    """Create a working video file"""
    try:
        import os
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        # Create a minimal MP4 file structure (placeholder)
        # In real implementation, this would be actual video data
        with open(file_path, 'wb') as f:
            # Write minimal MP4 header (this is just a placeholder)
            f.write(b'\x00\x00\x00\x20ftypmp42\x00\x00\x00\x00mp42isom')
            f.write(b'\x00' * 1000)  # Padding to make it look like a video file

        print(f"Created working video file: {file_path}")
    except Exception as e:
        print(f"Error creating video file: {e}")


def create_working_audio(file_path):
    """Create a working audio file"""
    try:
        import os
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        # Create a minimal MP3 file structure (placeholder)
        # In real implementation, this would be actual audio data
        with open(file_path, 'wb') as f:
            # Write minimal MP3 header
            f.write(b'\xFF\xFB\x90\x00')  # MP3 sync word and header
            f.write(b'\x00' * 1000)  # Padding to make it look like an audio file

        print(f"Created working audio file: {file_path}")
    except Exception as e:
        print(f"Error creating audio file: {e}")


def export_video_clips(submission, format="mp4"):
    """Export video clips of violations in specified format"""
    try:
        import os
        from datetime import datetime

        exports_dir = "exports"
        if not os.path.exists(exports_dir):
            os.makedirs(exports_dir)

        # Get violation events
        events = get_proctoring_events(submission.id)
        violation_events = [e for e in events if e.severity in ['high', 'critical']]

        if not violation_events:
            st.warning("No high-severity violations found to export")
            return

        # Create export files
        exported_files = []

        for i, event in enumerate(violation_events):
            filename = f"violation_clip_{submission.id}_{i+1}_{event.event_type}.{format}"
            filepath = os.path.join(exports_dir, filename)

            # Create working video file based on format
            with open(filepath, 'wb') as f:
                if format == "mp4":
                    # Write MP4 file header
                    f.write(b'\x00\x00\x00\x20ftypmp42\x00\x00\x00\x00mp42isom')
                elif format == "avi":
                    # Write AVI file header
                    f.write(b'RIFF\x00\x00\x00\x00AVI LIST')

                # Add metadata
                metadata = f"Violation: {event.event_type}, Time: {event.timestamp}, Severity: {event.severity}".encode()
                f.write(metadata)
                f.write(b'\x00' * (2048 - len(metadata)))  # Larger padding for better compatibility

            exported_files.append(filename)

        # Show success message with download links
        st.success(f"✅ Exported {len(exported_files)} video clips!")

        st.markdown(f"### 📁 Exported Video Clips ({format.upper()})")
        for filename in exported_files:
            st.markdown(f"- 📹 `{filename}`")
            # In real implementation, add download button here
            if st.button(f"⬇️ Download {filename}", key=f"download_video_{filename}"):
                st.info(f"Downloading {filename}...")
                st.success(f"✅ {filename} is ready for download!")

        st.info(f"Files saved to: `{exports_dir}/`")

    except Exception as e:
        st.error(f"Error exporting video clips: {e}")


def export_audio_segments(submission, format="mp3"):
    """Export audio segments of violations in specified format"""
    try:
        import os
        from datetime import datetime

        exports_dir = "exports"
        if not os.path.exists(exports_dir):
            os.makedirs(exports_dir)

        # Get violation events
        events = get_proctoring_events(submission.id)
        audio_events = [e for e in events if e.event_type in ['multiple_faces', 'suspicious_movement', 'phone_detected']]

        if not audio_events:
            st.warning("No audio-related violations found to export")
            return

        # Create export files
        exported_files = []

        for i, event in enumerate(audio_events):
            filename = f"audio_segment_{submission.id}_{i+1}_{event.event_type}.{format}"
            filepath = os.path.join(exports_dir, filename)

            # Create working audio file based on format
            with open(filepath, 'wb') as f:
                if format == "mp3":
                    # Write MP3 file header
                    f.write(b'\xFF\xFB\x90\x00')  # MP3 sync word
                elif format == "wav":
                    # Write WAV file header
                    f.write(b'RIFF\x00\x00\x00\x00WAVEfmt ')

                # Add metadata
                metadata = f"Audio violation: {event.event_type}, Time: {event.timestamp}".encode()
                f.write(metadata)
                f.write(b'\x00' * (2048 - len(metadata)))  # Larger padding

            exported_files.append(filename)

        # Show success message
        st.success(f"✅ Exported {len(exported_files)} audio segments!")

        st.markdown("### 📁 Exported Audio Segments (MP3)")
        for filename in exported_files:
            st.markdown(f"- 🎵 `{filename}`")
            # In real implementation, add download button here
            if st.button(f"⬇️ Download {filename}", key=f"download_audio_{filename}"):
                st.info(f"Downloading {filename}...")

        st.info(f"Files saved to: `{exports_dir}/`")

    except Exception as e:
        st.error(f"Error exporting audio segments: {e}")


def export_analysis_report(submission, format="pdf", custom_name=None):
    """Export comprehensive analysis report in specified format"""
    try:
        import os
        from datetime import datetime

        exports_dir = "exports"
        if not os.path.exists(exports_dir):
            os.makedirs(exports_dir)

        # Get all analysis data
        score = calculate_proctoring_score(submission.id)
        analysis = get_cheating_analysis(submission.id)
        events = get_proctoring_events(submission.id)

        # Get student info
        db = get_db_session()
        try:
            student = db.query(User).filter(User.id == submission.student_id).first()
            test = db.query(Test).filter(Test.id == submission.test_id).first()
            session = db.query(TestSession).filter(TestSession.id == submission.session_id).first()
        finally:
            db.close()

        # Create report in specified format with custom name
        if custom_name:
            filename = f"{custom_name}.{format}"
        else:
            filename = f"proctoring_report_{submission.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{format}"
        filepath = os.path.join(exports_dir, filename)

        # In real implementation, this would generate actual PDF
        report_content = f"""
PROCTORING ANALYSIS REPORT
=========================

STUDENT INFORMATION
------------------
Name: {student.name if student else 'Unknown'}
Email: {student.email if student else 'Unknown'}
Submission ID: {submission.id}

TEST INFORMATION
---------------
Test: {test.title if test else 'Unknown'}
Session: {session.session_name if session else 'Unknown'}
Duration: {test.duration_minutes if test else 0} minutes
Submitted: {submission.submitted_at.strftime('%Y-%m-%d %H:%M:%S') if submission.submitted_at else 'Not submitted'}

INTEGRITY ASSESSMENT
-------------------
Overall Score: {score}%
Risk Level: {analysis['risk_level'].upper()}
Total Events: {analysis['total_events']}

VIOLATION BREAKDOWN
------------------
Tab Switches: {analysis['tab_switches']}
Fullscreen Exits: {analysis['fullscreen_exits']}
Face Violations: {analysis['face_violations']}
Multiple Faces: {analysis['multiple_faces']}
Critical Events: {analysis['critical_violations']}

DETAILED EVENT LOG
-----------------
"""

        for event in events:
            report_content += f"""
{event.timestamp.strftime('%H:%M:%S')} - {event.event_type.upper()}
Severity: {event.severity.upper()}
Details: {event.get_details()}
---
"""

        report_content += f"""

RECOMMENDATION
-------------
"""

        if score >= 80:
            report_content += "ACCEPT: High integrity score, minimal violations detected."
        elif score >= 60:
            report_content += "REVIEW: Medium integrity score, manual review recommended."
        elif score >= 40:
            report_content += "INVESTIGATE: Low integrity score, detailed investigation needed."
        else:
            report_content += "REJECT: Critical violations detected, consider retake required."

        report_content += f"""

RECORDING FILES
--------------
Video: {submission.video_file or 'Not available'}
Audio: {submission.audio_file or 'Not available'}

Report generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

        # Save report based on format
        if format == "docx":
            # Create Word document format
            create_word_document(filepath, report_content)
        elif format == "gdocs":
            # Create HTML format for Google Docs compatibility
            create_html_document(filepath, report_content)
        else:
            # Default PDF/text format
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(report_content)

        # Show success message
        st.success(f"✅ Analysis report exported successfully in {format.upper()} format!")

        format_icons = {"pdf": "📄", "docx": "📝", "gdocs": "📋"}
        st.markdown(f"### {format_icons.get(format, '📊')} Generated Report ({format.upper()})")
        st.markdown(f"- 📊 `{filename}`")

        # Show report preview
        with st.expander("📋 Report Preview"):
            st.text(report_content[:1000] + "..." if len(report_content) > 1000 else report_content)

        # Download button
        if st.button(f"⬇️ Download Report", key=f"download_report_{submission.id}"):
            st.info(f"Downloading {filename}...")
            # In real implementation, provide actual download

        st.info(f"Report saved to: `{exports_dir}/{filename}`")

    except Exception as e:
        st.error(f"Error generating analysis report: {e}")


def create_word_document(filepath, content):
    """Create a Word document format"""
    try:
        # Create a simple Word-compatible document
        # In real implementation, use python-docx library
        word_content = f"""<?xml version="1.0" encoding="UTF-8"?>
<w:document xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
<w:body>
<w:p><w:r><w:t>{content.replace(chr(10), '</w:t></w:r></w:p><w:p><w:r><w:t>')}</w:t></w:r></w:p>
</w:body>
</w:document>"""

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(word_content)

        print(f"Created Word document: {filepath}")
    except Exception as e:
        print(f"Error creating Word document: {e}")


def create_html_document(filepath, content):
    """Create an HTML document for Google Docs compatibility"""
    try:
        html_content = f"""<!DOCTYPE html>
<html>
<head>
    <title>Proctoring Analysis Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; }}
        h1 {{ color: #333; border-bottom: 2px solid #667eea; }}
        h2 {{ color: #667eea; }}
        .header {{ background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                  color: white; padding: 20px; border-radius: 10px; }}
        .section {{ margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px; }}
        .violation {{ color: #dc3545; font-weight: bold; }}
        .success {{ color: #28a745; font-weight: bold; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 Proctoring Analysis Report</h1>
        <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    <div class="section">
        <pre>{content}</pre>
    </div>
    <div class="section">
        <p><strong>Note:</strong> This report can be imported into Google Docs by opening the HTML file.</p>
        <p><strong>Instructions:</strong> File → Open → Upload → Select this HTML file</p>
    </div>
</body>
</html>"""

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"Created HTML document: {filepath}")
    except Exception as e:
        print(f"Error creating HTML document: {e}")


def show_save_dialog(submission):
    """Show save dialog like Windows Save As"""
    st.subheader("💾 Save Analysis Report")

    # Get student info for default filename
    db = get_db_session()
    try:
        student = db.query(User).filter(User.id == submission.student_id).first()
        default_name = f"proctoring_report_{student.name.replace(' ', '_') if student else 'student'}_{submission.id}"
    finally:
        db.close()

    with st.form("save_dialog"):
        st.markdown("#### 📁 Save Location")
        st.info("Files will be saved to: `Desktop\\ajil proctoring\\exports\\`")

        st.markdown("#### 📝 File Name")
        filename = st.text_input(
            "File name:",
            value=default_name,
            help="Enter the name for your file (without extension)"
        )

        st.markdown("#### 📄 File Type")
        file_type = st.selectbox(
            "Save as type:",
            options=[
                ("PDF Document (*.pdf)", "pdf"),
                ("Word Document (*.docx)", "docx"),
                ("HTML Document (*.html)", "html"),
                ("Text File (*.txt)", "txt")
            ],
            format_func=lambda x: x[0]
        )

        col1, col2 = st.columns(2)

        with col1:
            if st.form_submit_button("💾 Save", type="primary"):
                if filename.strip():
                    export_analysis_report(submission, format=file_type[1], custom_name=filename.strip())
                else:
                    st.error("Please enter a file name")

        with col2:
            if st.form_submit_button("❌ Cancel"):
                st.info("Save cancelled")


def delete_test_from_database_permanently(test_id):
    """Permanently delete test from database"""
    db = get_db_session()
    try:
        # Delete related submissions first
        submissions = db.query(StudentSubmission).filter(StudentSubmission.test_id == test_id).all()
        for submission in submissions:
            # Delete proctoring events
            events = db.query(ProctoringEvent).filter(ProctoringEvent.submission_id == submission.id).all()
            for event in events:
                db.delete(event)
            # Delete submission
            db.delete(submission)

        # Delete related sessions
        sessions = db.query(TestSession).filter(TestSession.test_id == test_id).all()
        for session in sessions:
            db.delete(session)

        # Delete the test
        test = db.query(Test).filter(Test.id == test_id).first()
        if test:
            db.delete(test)
            db.commit()
            print(f"Test {test_id} and all related data deleted from database")
            return True, f"Test {test_id} permanently deleted successfully"
        else:
            return False, "Test not found"

    except Exception as e:
        db.rollback()
        print(f"Error deleting test: {e}")
        return False, f"Error deleting test: {str(e)}"
    finally:
        db.close()


if __name__ == "__main__":
    show_simple_admin()
