"""
Test script for Enhanced Background Proctoring System
Demonstrates the improved face detection, tracking, and liveness detection
"""
import time
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_system_check():
    """Test the pre-test system check functionality"""
    print("\n" + "="*50)
    print("TESTING ENHANCED SYSTEM CHECK")
    print("="*50)
    
    try:
        from enhanced_background_proctor import perform_enhanced_system_check
        results = perform_enhanced_system_check()
        
        print(f"Overall Score: {results['overall_score']:.2f}/1.0")
        print("\nDetailed Results:")
        
        for check_name, result in results.items():
            if isinstance(result, dict) and 'score' in result:
                print(f"  {check_name.replace('_', ' ').title()}: {result['score']:.2f} - {result['message']}")
        
        return results['overall_score'] > 0.7
    except Exception as e:
        print(f"❌ System check failed: {e}")
        return False


def test_enhanced_proctoring():
    """Test the enhanced proctoring functionality"""
    print("\n" + "="*50)
    print("TESTING ENHANCED PROCTORING SESSION")
    print("="*50)
    
    test_token = f"test_session_{int(time.time())}"
    
    try:
        from enhanced_background_proctor import (
            start_enhanced_proctoring_session,
            stop_enhanced_proctoring_session,
            get_enhanced_session_status
        )
        
        # Start enhanced proctoring session
        print(f"Starting enhanced proctoring session: {test_token}")
        if not start_enhanced_proctoring_session(test_token):
            print("❌ Failed to start enhanced proctoring session")
            return False
        
        print("✅ Enhanced proctoring session started successfully")
        
        # Monitor for 15 seconds (shorter for testing)
        print("Monitoring for 15 seconds...")
        for i in range(15):
            time.sleep(1)
            
            # Get status every 5 seconds
            if i % 5 == 0:
                status = get_enhanced_session_status(test_token)
                if status:
                    print(f"  Status update ({i}s):")
                    print(f"    Faces detected: {status['metrics']['face_count']}")
                    print(f"    Tracked faces: {status['metrics']['tracked_faces']}")
                    print(f"    Attention score: {status['metrics']['attention_score']:.2f}")
                    print(f"    Liveness score: {status['metrics']['liveness_score']:.2f}")
                    print(f"    Risk score: {status['risk_score']:.2f}")
                    print(f"    Violations: {status['violation_count']}")
                    print(f"    Processing FPS: {status['performance']['fps']:.1f}")
        
        # Get final status
        final_status = get_enhanced_session_status(test_token)
        if final_status:
            print("\nFinal Session Summary:")
            print(f"  Total frames processed: {final_status['frame_count']}")
            print(f"  Total violations: {final_status['violation_count']}")
            print(f"  Final attention score: {final_status['metrics']['attention_score']:.2f}")
            print(f"  Final liveness score: {final_status['metrics']['liveness_score']:.2f}")
            print(f"  Final risk score: {final_status['risk_score']:.2f}")
            print(f"  Average processing time: {final_status['performance']['avg_processing_time']:.3f}s")
        
        # Stop session
        print(f"\nStopping enhanced proctoring session: {test_token}")
        if stop_enhanced_proctoring_session(test_token):
            print("✅ Enhanced proctoring session stopped successfully")
            return True
        else:
            print("❌ Failed to stop enhanced proctoring session")
            return False
            
    except Exception as e:
        logger.error(f"Error during enhanced proctoring test: {e}")
        # Try to stop session if it was started
        try:
            stop_enhanced_proctoring_session(test_token)
        except:
            pass
        return False


def test_violation_detection():
    """Test violation detection with contextual logic"""
    print("\n" + "="*50)
    print("TESTING VIOLATION DETECTION")
    print("="*50)
    
    test_token = f"violation_test_{int(time.time())}"
    
    try:
        from enhanced_background_proctor import (
            enhanced_proctor_manager,
            start_enhanced_proctoring_session,
            stop_enhanced_proctoring_session,
            get_enhanced_session_status
        )
        
        if not start_enhanced_proctoring_session(test_token):
            print("❌ Failed to start session for violation testing")
            return False
        
        print("✅ Session started for violation testing")
        
        # Wait for initial setup
        time.sleep(3)
        
        # Simulate browser events to test contextual logic
        print("Simulating browser violations...")
        
        # Test tab switch with grace period
        print("  Testing tab switch violations (should respect grace period)...")
        enhanced_proctor_manager.log_browser_event(test_token, 'tab_switch', {'test': 'first_switch'})
        time.sleep(1)
        enhanced_proctor_manager.log_browser_event(test_token, 'tab_switch', {'test': 'second_switch_grace'})
        time.sleep(6)  # Wait for grace period to expire
        enhanced_proctor_manager.log_browser_event(test_token, 'tab_switch', {'test': 'third_switch_after_grace'})
        
        # Test fullscreen exit
        print("  Testing fullscreen exit violations...")
        enhanced_proctor_manager.log_browser_event(test_token, 'fullscreen_exit', {'test': 'fullscreen_test'})
        
        # Wait and check status
        time.sleep(5)
        status = get_enhanced_session_status(test_token)
        if status:
            print(f"  Violations logged: {status['violation_count']}")
            print(f"  Current risk score: {status['risk_score']:.2f}")
        
        # Stop session
        stop_enhanced_proctoring_session(test_token)
        print("✅ Violation detection test completed")
        return True
        
    except Exception as e:
        logger.error(f"Error during violation detection test: {e}")
        try:
            stop_enhanced_proctoring_session(test_token)
        except:
            pass
        return False


def compare_with_original():
    """Compare enhanced system with original system"""
    print("\n" + "="*50)
    print("ENHANCED VS ORIGINAL COMPARISON")
    print("="*50)
    
    print("Enhanced System Features:")
    print("✅ MediaPipe Face Mesh (vs OpenCV Haar Cascade)")
    print("✅ Face tracking with unique IDs")
    print("✅ Liveness detection via blink analysis")
    print("✅ Enhanced gaze tracking")
    print("✅ Contextual violation logic with grace periods")
    print("✅ Burst detection for repeated violations")
    print("✅ Pre-test system check")
    print("✅ Advanced image preprocessing")
    print("✅ Performance monitoring")
    print("✅ Risk score calculation")
    
    print("\nExpected Improvements:")
    print("📈 Reduced false positives for face detection")
    print("📈 Better handling of lighting variations")
    print("📈 More accurate multiple face detection")
    print("📈 Liveness verification against photos/videos")
    print("📈 Smarter violation thresholds")
    print("📈 Better system reliability assessment")


def integration_guide():
    """Show integration guide for the enhanced system"""
    print("\n" + "="*50)
    print("INTEGRATION GUIDE")
    print("="*50)
    
    print("To integrate the enhanced proctoring system:")
    print("\n1. Install required dependencies:")
    print("   pip install mediapipe")
    
    print("\n2. Update imports in your main application:")
    print("   Replace: from background_proctor import ...")
    print("   With:    from enhanced_background_proctor import ...")
    
    print("\n3. Update function calls:")
    print("   Replace: start_proctoring_session(token)")
    print("   With:    start_enhanced_proctoring_session(token)")
    
    print("\n4. Add pre-test system check:")
    print("   results = perform_enhanced_system_check()")
    print("   if results['overall_score'] < 0.7:")
    print("       # Show warning to student")
    
    print("\n5. Monitor enhanced metrics:")
    print("   status = get_enhanced_session_status(token)")
    print("   # Access: liveness_score, risk_score, tracked_faces, etc.")


def main():
    """Main test function"""
    print("ENHANCED BACKGROUND PROCTORING SYSTEM TEST")
    print("="*60)
    
    # Test system check
    system_ok = test_system_check()
    if not system_ok:
        print("\n⚠️  System check indicates potential issues. Proceeding with caution...")
    
    # Test enhanced proctoring
    proctoring_ok = test_enhanced_proctoring()
    
    # Test violation detection
    violation_ok = test_violation_detection()
    
    # Show comparison
    compare_with_original()
    
    # Show integration guide
    integration_guide()
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    print(f"System Check: {'✅ PASS' if system_ok else '❌ ISSUES'}")
    print(f"Enhanced Proctoring: {'✅ PASS' if proctoring_ok else '❌ FAIL'}")
    print(f"Violation Detection: {'✅ PASS' if violation_ok else '❌ FAIL'}")
    
    if all([proctoring_ok, violation_ok]):
        print("\n🎉 All tests passed! Enhanced proctoring system is ready for use.")
        print("\nNext steps:")
        print("1. Review the enhanced_background_proctor.log file")
        print("2. Integrate the enhanced system into your application")
        print("3. Test with real students and monitor improvements")
    else:
        print("\n⚠️  Some tests failed. Please check the logs and fix issues before deployment.")


if __name__ == "__main__":
    main()
