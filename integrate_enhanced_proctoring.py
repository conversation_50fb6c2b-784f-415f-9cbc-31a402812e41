"""
Integration script to upgrade existing proctoring system to enhanced version
"""
import os
import shutil
from pathlib import Path


def backup_original_files():
    """Backup original proctoring files"""
    print("📦 Creating backup of original files...")
    
    backup_dir = Path("backup_original_proctoring")
    backup_dir.mkdir(exist_ok=True)
    
    files_to_backup = [
        "background_proctor.py",
        "student_test_interface.py"
    ]
    
    for file_name in files_to_backup:
        if Path(file_name).exists():
            shutil.copy2(file_name, backup_dir / file_name)
            print(f"✅ Backed up {file_name}")
        else:
            print(f"⚠️  {file_name} not found")
    
    print(f"✅ Backup completed in {backup_dir}")


def update_student_interface():
    """Update student test interface to use enhanced proctoring"""
    print("\n🔧 Updating student test interface...")
    
    interface_file = Path("student_test_interface.py")
    if not interface_file.exists():
        print("❌ student_test_interface.py not found")
        return False
    
    # Read current content
    with open(interface_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Add enhanced proctoring import
    if "from enhanced_background_proctor import" not in content:
        # Find the import section and add enhanced import
        import_section = "from database_models import"
        if import_section in content:
            enhanced_import = """
# Enhanced proctoring system
try:
    from enhanced_background_proctor import (
        start_enhanced_proctoring_session,
        stop_enhanced_proctoring_session,
        get_enhanced_session_status,
        perform_enhanced_system_check
    )
    ENHANCED_PROCTORING_AVAILABLE = True
except ImportError:
    print("Enhanced proctoring not available, falling back to basic proctoring")
    ENHANCED_PROCTORING_AVAILABLE = False
"""
            content = content.replace(import_section, import_section + enhanced_import)
            print("✅ Added enhanced proctoring imports")
    
    # Add system check function
    system_check_function = '''
def show_system_check():
    """Show pre-test system check"""
    if not ENHANCED_PROCTORING_AVAILABLE:
        return True
    
    st.markdown("### 🔍 System Check")
    st.info("Checking your system for optimal proctoring performance...")
    
    with st.spinner("Running system check..."):
        results = perform_enhanced_system_check()
    
    overall_score = results.get('overall_score', 0)
    
    if overall_score >= 0.8:
        st.success(f"✅ System check passed! Score: {overall_score:.2f}/1.0")
        return True
    elif overall_score >= 0.6:
        st.warning(f"⚠️ System check shows some issues. Score: {overall_score:.2f}/1.0")
        
        # Show detailed results
        with st.expander("View detailed results"):
            for check_name, result in results.items():
                if isinstance(result, dict) and 'score' in result:
                    score = result['score']
                    message = result['message']
                    
                    if score >= 0.8:
                        st.success(f"✅ {check_name.replace('_', ' ').title()}: {message}")
                    elif score >= 0.6:
                        st.warning(f"⚠️ {check_name.replace('_', ' ').title()}: {message}")
                    else:
                        st.error(f"❌ {check_name.replace('_', ' ').title()}: {message}")
        
        return st.button("Continue anyway", type="secondary")
    else:
        st.error(f"❌ System check failed! Score: {overall_score:.2f}/1.0")
        
        # Show detailed results
        with st.expander("View detailed results"):
            for check_name, result in results.items():
                if isinstance(result, dict) and 'score' in result:
                    score = result['score']
                    message = result['message']
                    st.error(f"❌ {check_name.replace('_', ' ').title()}: {message}")
        
        st.info("Please fix the issues above before taking the test.")
        return False
'''
    
    if "def show_system_check():" not in content:
        # Add the function before the main interface function
        main_function_start = "def show_student_test_interface():"
        if main_function_start in content:
            content = content.replace(main_function_start, system_check_function + "\n\n" + main_function_start)
            print("✅ Added system check function")
    
    # Write updated content
    with open(interface_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Student interface updated successfully")
    return True


def create_enhanced_launcher():
    """Create a launcher script for the enhanced system"""
    print("\n🚀 Creating enhanced system launcher...")
    
    launcher_content = '''"""
Enhanced Proctoring System Launcher
Starts the system with enhanced face detection and monitoring
"""
import subprocess
import sys
import time
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'mediapipe',
        'opencv-python',
        'streamlit',
        'sqlalchemy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing required packages: {', '.join(missing_packages)}")
        print("Install them with: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ All required packages are installed")
    return True

def start_enhanced_system():
    """Start the enhanced proctoring system"""
    print("🚀 Starting Enhanced Proctoring System...")
    
    if not check_dependencies():
        return False
    
    # Test enhanced proctoring
    print("🧪 Testing enhanced proctoring system...")
    try:
        result = subprocess.run([sys.executable, "enhanced_proctoring_test.py"], 
                              capture_output=True, text=True, timeout=60)
        if result.returncode == 0:
            print("✅ Enhanced proctoring test passed")
        else:
            print("⚠️ Enhanced proctoring test had issues")
            print(result.stdout)
            print(result.stderr)
    except subprocess.TimeoutExpired:
        print("⚠️ Enhanced proctoring test timed out")
    except Exception as e:
        print(f"⚠️ Could not run enhanced proctoring test: {e}")
    
    # Start the main application
    print("🌐 Starting main application...")
    try:
        subprocess.run([sys.executable, "-m", "streamlit", "run", "main_app.py"])
    except KeyboardInterrupt:
        print("\\n👋 Enhanced proctoring system stopped")
    except Exception as e:
        print(f"❌ Error starting main application: {e}")

if __name__ == "__main__":
    start_enhanced_system()
'''
    
    with open("start_enhanced_system.py", 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print("✅ Enhanced launcher created: start_enhanced_system.py")


def create_requirements_update():
    """Update requirements.txt with enhanced proctoring dependencies"""
    print("\n📋 Updating requirements...")
    
    enhanced_requirements = [
        "mediapipe>=0.10.0",
        "opencv-python>=4.8.0",
        "numpy>=1.21.0"
    ]
    
    requirements_file = Path("requirements.txt")
    
    if requirements_file.exists():
        with open(requirements_file, 'r') as f:
            existing_requirements = f.read()
        
        # Add new requirements if not already present
        updated_requirements = existing_requirements
        for req in enhanced_requirements:
            package_name = req.split('>=')[0]
            if package_name not in existing_requirements:
                updated_requirements += f"\n{req}"
                print(f"✅ Added {req}")
        
        with open(requirements_file, 'w') as f:
            f.write(updated_requirements)
    else:
        # Create new requirements file
        with open(requirements_file, 'w') as f:
            f.write("\n".join(enhanced_requirements))
        print("✅ Created new requirements.txt")


def show_integration_summary():
    """Show summary of integration steps"""
    print("\n" + "="*60)
    print("ENHANCED PROCTORING INTEGRATION COMPLETE")
    print("="*60)
    
    print("\n✅ What was done:")
    print("1. Backed up original files")
    print("2. Updated student test interface")
    print("3. Created enhanced system launcher")
    print("4. Updated requirements.txt")
    
    print("\n🚀 Next steps:")
    print("1. Install new dependencies: pip install -r requirements.txt")
    print("2. Test the enhanced system: python enhanced_proctoring_test.py")
    print("3. Start the enhanced system: python start_enhanced_system.py")
    print("4. Monitor logs: tail -f enhanced_background_proctor.log")
    
    print("\n📊 Expected improvements:")
    print("• More accurate face detection")
    print("• Reduced false positives")
    print("• Better handling of lighting variations")
    print("• Liveness detection against photos/videos")
    print("• Smarter violation thresholds")
    print("• Pre-test system reliability check")
    
    print("\n⚠️  Important notes:")
    print("• Original files are backed up in backup_original_proctoring/")
    print("• Enhanced system requires MediaPipe (will be installed)")
    print("• Monitor the enhanced_background_proctor.log for detailed insights")
    print("• Test thoroughly before deploying to production")


def main():
    """Main integration function"""
    print("ENHANCED PROCTORING SYSTEM INTEGRATION")
    print("="*50)
    
    # Backup original files
    backup_original_files()
    
    # Update student interface
    update_student_interface()
    
    # Create enhanced launcher
    create_enhanced_launcher()
    
    # Update requirements
    create_requirements_update()
    
    # Show summary
    show_integration_summary()


if __name__ == "__main__":
    main()
