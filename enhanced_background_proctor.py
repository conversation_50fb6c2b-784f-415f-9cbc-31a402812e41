"""
Enhanced Background Proctoring Service
Advanced face detection, tracking, and liveness detection using MediaPipe
"""
import cv2
import time
import threading
import logging
import json
import numpy as np
import mediapipe as mp
from datetime import datetime, timezone
from pathlib import Path
from collections import defaultdict, deque
from typing import Dict, List, Tuple, Optional, Any
import math

# Import our database models and utilities
from database_models import (
    log_monitoring_event, record_session_metrics,
    get_session_by_token, ProctorSession, get_db_session
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_background_proctor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class FaceTracker:
    """Advanced face tracking with unique IDs and liveness detection"""
    
    def __init__(self):
        self.tracked_faces = {}
        self.next_face_id = 1
        self.face_history = defaultdict(lambda: deque(maxlen=30))  # 30 frames history
        self.blink_detector = BlinkDetector()
        
    def update_faces(self, face_landmarks, frame_time):
        """Update tracked faces with new detections"""
        current_faces = {}
        
        if face_landmarks:
            for i, landmarks in enumerate(face_landmarks):
                # Calculate face center for tracking
                face_center = self._get_face_center(landmarks)
                
                # Find closest existing face or create new one
                face_id = self._match_or_create_face(face_center, frame_time)
                
                # Update face data
                current_faces[face_id] = {
                    'landmarks': landmarks,
                    'center': face_center,
                    'last_seen': frame_time,
                    'blink_data': self.blink_detector.detect_blink(landmarks)
                }
                
                # Update history
                self.face_history[face_id].append({
                    'time': frame_time,
                    'center': face_center,
                    'landmarks': landmarks
                })
        
        # Remove old faces (not seen for 2 seconds)
        self._cleanup_old_faces(frame_time)
        
        # Update tracked faces
        self.tracked_faces.update(current_faces)
        
        return current_faces
    
    def _get_face_center(self, landmarks):
        """Calculate face center from landmarks"""
        # Use nose tip (landmark 1) as face center
        nose_tip = landmarks.landmark[1]
        return (nose_tip.x, nose_tip.y)
    
    def _match_or_create_face(self, face_center, frame_time):
        """Match face to existing tracked face or create new one"""
        min_distance = float('inf')
        matched_id = None
        
        # Try to match with existing faces
        for face_id, face_data in self.tracked_faces.items():
            if frame_time - face_data['last_seen'] < 2.0:  # Face seen within 2 seconds
                distance = self._calculate_distance(face_center, face_data['center'])
                if distance < 0.1 and distance < min_distance:  # Threshold for same face
                    min_distance = distance
                    matched_id = face_id
        
        if matched_id is None:
            # Create new face
            matched_id = self.next_face_id
            self.next_face_id += 1
            logger.info(f"New face detected with ID: {matched_id}")
        
        return matched_id
    
    def _calculate_distance(self, center1, center2):
        """Calculate Euclidean distance between two face centers"""
        return math.sqrt((center1[0] - center2[0])**2 + (center1[1] - center2[1])**2)
    
    def _cleanup_old_faces(self, current_time):
        """Remove faces not seen for more than 2 seconds"""
        to_remove = []
        for face_id, face_data in self.tracked_faces.items():
            if current_time - face_data['last_seen'] > 2.0:
                to_remove.append(face_id)
        
        for face_id in to_remove:
            del self.tracked_faces[face_id]
            if face_id in self.face_history:
                del self.face_history[face_id]
            logger.info(f"Removed old face with ID: {face_id}")
    
    def get_active_face_count(self):
        """Get number of currently active faces"""
        return len(self.tracked_faces)
    
    def is_new_face_detected(self, previous_count):
        """Check if a new face appeared"""
        current_count = self.get_active_face_count()
        return current_count > previous_count


class BlinkDetector:
    """Detect eye blinks for liveness verification"""
    
    def __init__(self):
        self.blink_threshold = 0.25
        self.consecutive_frames = 3
        self.blink_history = deque(maxlen=100)  # Store last 100 blink events
        
    def detect_blink(self, landmarks):
        """Detect blink from face landmarks"""
        try:
            # Get eye landmarks (MediaPipe face mesh indices)
            left_eye = self._get_eye_aspect_ratio(landmarks, 'left')
            right_eye = self._get_eye_aspect_ratio(landmarks, 'right')
            
            # Average eye aspect ratio
            ear = (left_eye + right_eye) / 2.0
            
            # Detect blink
            is_blink = ear < self.blink_threshold
            
            # Record blink event
            current_time = time.time()
            self.blink_history.append({
                'time': current_time,
                'ear': ear,
                'is_blink': is_blink
            })
            
            return {
                'ear': ear,
                'is_blink': is_blink,
                'blink_rate': self._calculate_blink_rate()
            }
            
        except Exception as e:
            logger.error(f"Error detecting blink: {e}")
            return {'ear': 0.3, 'is_blink': False, 'blink_rate': 0}
    
    def _get_eye_aspect_ratio(self, landmarks, eye):
        """Calculate Eye Aspect Ratio (EAR) for blink detection"""
        # Simplified EAR calculation using key eye landmarks
        # This is a basic implementation - can be enhanced with proper eye landmark indices
        try:
            if eye == 'left':
                # Left eye landmarks (approximate indices)
                p1, p2, p3, p4, p5, p6 = 33, 7, 163, 144, 145, 153
            else:
                # Right eye landmarks (approximate indices)
                p1, p2, p3, p4, p5, p6 = 362, 382, 381, 380, 374, 373
            
            # Get landmark coordinates
            points = []
            for idx in [p1, p2, p3, p4, p5, p6]:
                if idx < len(landmarks.landmark):
                    point = landmarks.landmark[idx]
                    points.append((point.x, point.y))
            
            if len(points) == 6:
                # Calculate EAR
                A = math.sqrt((points[1][0] - points[5][0])**2 + (points[1][1] - points[5][1])**2)
                B = math.sqrt((points[2][0] - points[4][0])**2 + (points[2][1] - points[4][1])**2)
                C = math.sqrt((points[0][0] - points[3][0])**2 + (points[0][1] - points[3][1])**2)
                
                ear = (A + B) / (2.0 * C)
                return ear
            
        except Exception as e:
            logger.error(f"Error calculating EAR for {eye} eye: {e}")
        
        return 0.3  # Default value
    
    def _calculate_blink_rate(self):
        """Calculate blinks per minute"""
        if len(self.blink_history) < 2:
            return 0
        
        current_time = time.time()
        minute_ago = current_time - 60
        
        # Count blinks in the last minute
        blinks_in_minute = 0
        for event in self.blink_history:
            if event['time'] > minute_ago and event['is_blink']:
                blinks_in_minute += 1
        
        return blinks_in_minute


class GazeTracker:
    """Enhanced gaze tracking using MediaPipe face landmarks"""
    
    def __init__(self):
        self.gaze_history = deque(maxlen=30)
        self.looking_away_threshold = 0.3
        
    def estimate_gaze(self, landmarks):
        """Estimate gaze direction from face landmarks"""
        try:
            # Get key facial landmarks for gaze estimation
            nose_tip = landmarks.landmark[1]
            left_eye = landmarks.landmark[33]
            right_eye = landmarks.landmark[362]
            
            # Calculate gaze direction (simplified)
            eye_center_x = (left_eye.x + right_eye.x) / 2
            eye_center_y = (left_eye.y + right_eye.y) / 2
            
            # Gaze deviation from center
            gaze_x = nose_tip.x - eye_center_x
            gaze_y = nose_tip.y - eye_center_y
            
            # Calculate gaze magnitude
            gaze_magnitude = math.sqrt(gaze_x**2 + gaze_y**2)
            
            # Determine if looking away
            is_looking_away = gaze_magnitude > self.looking_away_threshold
            
            gaze_data = {
                'gaze_x': gaze_x,
                'gaze_y': gaze_y,
                'magnitude': gaze_magnitude,
                'looking_away': is_looking_away,
                'timestamp': time.time()
            }
            
            self.gaze_history.append(gaze_data)
            
            return gaze_data
            
        except Exception as e:
            logger.error(f"Error estimating gaze: {e}")
            return {
                'gaze_x': 0, 'gaze_y': 0, 'magnitude': 0,
                'looking_away': False, 'timestamp': time.time()
            }
    
    def get_attention_score(self):
        """Calculate attention score based on recent gaze data"""
        if not self.gaze_history:
            return 0.5
        
        # Calculate percentage of time looking at screen
        looking_at_screen = sum(1 for g in self.gaze_history if not g['looking_away'])
        attention_score = looking_at_screen / len(self.gaze_history)
        
        return attention_score


class EnhancedBackgroundProctor:
    """Enhanced background proctoring with MediaPipe and advanced detection"""
    
    def __init__(self, session_token):
        self.session_token = session_token
        self.session = None
        self.running = False
        self.camera = None
        self.monitoring_thread = None
        
        # Initialize MediaPipe
        try:
            self.mp_face_mesh = mp.solutions.face_mesh
            self.face_mesh = self.mp_face_mesh.FaceMesh(
                max_num_faces=5,
                refine_landmarks=True,
                min_detection_confidence=0.7,
                min_tracking_confidence=0.5
            )
            logger.info("MediaPipe Face Mesh initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize MediaPipe: {e}")
            self.face_mesh = None
        
        # Initialize advanced components
        self.face_tracker = FaceTracker()
        self.gaze_tracker = GazeTracker()
        
        # Violation tracking with contextual logic
        self.violation_tracker = ViolationTracker()
        
        # Monitoring metrics
        self.metrics = {
            'face_detected': False,
            'face_count': 0,
            'tracked_faces': 0,
            'attention_score': 0.0,
            'looking_away': False,
            'blink_rate': 0,
            'liveness_score': 0.0,
            'tab_switches': 0,
            'fullscreen_exits': 0
        }
        
        # Performance tracking
        self.frame_count = 0
        self.processing_times = deque(maxlen=100)
        
    def preprocess_frame(self, frame):
        """Enhanced frame preprocessing for better detection"""
        try:
            # Histogram equalization for better contrast
            lab = cv2.cvtColor(frame, cv2.COLOR_BGR2LAB)
            lab[:,:,0] = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8)).apply(lab[:,:,0])
            frame = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
            
            # Noise reduction
            frame = cv2.GaussianBlur(frame, (3, 3), 0)
            
            return frame
            
        except Exception as e:
            logger.error(f"Error preprocessing frame: {e}")
            return frame


class ViolationTracker:
    """Advanced violation tracking with contextual logic and thresholds"""

    def __init__(self):
        self.violation_history = deque(maxlen=1000)
        self.grace_periods = {
            'tab_switch': 5.0,  # 5 seconds grace period
            'fullscreen_exit': 3.0,
            'face_lost': 3.0,
            'multiple_faces': 2.0
        }
        self.burst_thresholds = {
            'tab_switch': 3,  # 3 violations in 10 seconds
            'fullscreen_exit': 2,
            'face_lost': 2
        }
        self.burst_window = 10.0  # 10 seconds

    def should_log_violation(self, violation_type, current_time):
        """Determine if violation should be logged based on context"""
        # Check grace period
        recent_violations = [
            v for v in self.violation_history
            if v['type'] == violation_type and
            current_time - v['timestamp'] < self.grace_periods.get(violation_type, 1.0)
        ]

        if recent_violations:
            return False  # Still in grace period

        # Check for burst detection
        window_violations = [
            v for v in self.violation_history
            if v['type'] == violation_type and
            current_time - v['timestamp'] < self.burst_window
        ]

        threshold = self.burst_thresholds.get(violation_type, 1)
        if len(window_violations) >= threshold:
            return True  # Burst detected - log as high severity

        return True  # Normal violation

    def log_violation(self, violation_type, severity='medium', data=None):
        """Log violation with timestamp"""
        violation = {
            'type': violation_type,
            'severity': severity,
            'timestamp': time.time(),
            'data': data or {}
        }
        self.violation_history.append(violation)
        return violation

    def get_risk_score(self):
        """Calculate overall risk score based on violation patterns"""
        if not self.violation_history:
            return 0.0

        current_time = time.time()
        recent_window = 300  # 5 minutes

        recent_violations = [
            v for v in self.violation_history
            if current_time - v['timestamp'] < recent_window
        ]

        if not recent_violations:
            return 0.0

        # Weight violations by severity and recency
        risk_score = 0.0
        for violation in recent_violations:
            severity_weight = {'low': 0.1, 'medium': 0.5, 'high': 1.0}.get(violation['severity'], 0.5)
            time_weight = 1.0 - (current_time - violation['timestamp']) / recent_window
            risk_score += severity_weight * time_weight

        return min(risk_score, 1.0)


class SystemChecker:
    """Pre-test system check functionality"""

    def __init__(self):
        self.check_results = {}

    def perform_system_check(self, camera):
        """Perform comprehensive pre-test system check"""
        results = {
            'camera_quality': self._check_camera_quality(camera),
            'lighting_quality': self._check_lighting(camera),
            'face_detection': self._check_face_detection(camera),
            'stability': self._check_stability(camera),
            'overall_score': 0.0
        }

        # Calculate overall score
        scores = [r['score'] for r in results.values() if isinstance(r, dict) and 'score' in r]
        results['overall_score'] = sum(scores) / len(scores) if scores else 0.0

        self.check_results = results
        return results

    def _check_camera_quality(self, camera):
        """Check camera resolution and quality"""
        try:
            ret, frame = camera.read()
            if not ret:
                return {'score': 0.0, 'message': 'Camera not accessible'}

            height, width = frame.shape[:2]

            if width >= 640 and height >= 480:
                score = 1.0
                message = 'Camera quality excellent'
            elif width >= 320 and height >= 240:
                score = 0.7
                message = 'Camera quality acceptable'
            else:
                score = 0.3
                message = 'Camera quality poor - consider upgrading'

            return {'score': score, 'message': message, 'resolution': f'{width}x{height}'}

        except Exception as e:
            return {'score': 0.0, 'message': f'Camera check failed: {e}'}

    def _check_lighting(self, camera):
        """Check lighting conditions"""
        try:
            ret, frame = camera.read()
            if not ret:
                return {'score': 0.0, 'message': 'Cannot assess lighting'}

            # Convert to grayscale and calculate mean brightness
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            mean_brightness = np.mean(gray)

            if 80 <= mean_brightness <= 180:
                score = 1.0
                message = 'Lighting conditions excellent'
            elif 50 <= mean_brightness <= 220:
                score = 0.7
                message = 'Lighting conditions acceptable'
            else:
                score = 0.3
                message = 'Poor lighting - adjust room lighting'

            return {
                'score': score,
                'message': message,
                'brightness': float(mean_brightness)
            }

        except Exception as e:
            return {'score': 0.0, 'message': f'Lighting check failed: {e}'}

    def _check_face_detection(self, camera):
        """Check if face can be detected reliably"""
        try:
            # Initialize MediaPipe for testing
            mp_face_mesh = mp.solutions.face_mesh
            face_mesh = mp_face_mesh.FaceMesh(
                max_num_faces=1,
                min_detection_confidence=0.5
            )

            face_detected_frames = 0
            total_frames = 10

            for _ in range(total_frames):
                ret, frame = camera.read()
                if ret:
                    rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    results = face_mesh.process(rgb_frame)

                    if results.multi_face_landmarks:
                        face_detected_frames += 1

                time.sleep(0.1)  # Small delay between frames

            detection_rate = face_detected_frames / total_frames

            if detection_rate >= 0.8:
                score = 1.0
                message = 'Face detection excellent'
            elif detection_rate >= 0.5:
                score = 0.7
                message = 'Face detection acceptable'
            else:
                score = 0.3
                message = 'Face detection poor - adjust position/lighting'

            return {
                'score': score,
                'message': message,
                'detection_rate': detection_rate
            }

        except Exception as e:
            return {'score': 0.0, 'message': f'Face detection check failed: {e}'}

    def _check_stability(self, camera):
        """Check camera stability and connection"""
        try:
            stable_frames = 0
            total_frames = 5

            for _ in range(total_frames):
                ret, frame = camera.read()
                if ret:
                    stable_frames += 1
                time.sleep(0.2)

            stability_rate = stable_frames / total_frames

            if stability_rate == 1.0:
                score = 1.0
                message = 'Camera connection stable'
            elif stability_rate >= 0.8:
                score = 0.7
                message = 'Camera connection mostly stable'
            else:
                score = 0.3
                message = 'Camera connection unstable'

            return {
                'score': score,
                'message': message,
                'stability_rate': stability_rate
            }

        except Exception as e:
            return {'score': 0.0, 'message': f'Stability check failed: {e}'}


# Continue with the main EnhancedBackgroundProctor class methods
def initialize_session(self):
    """Initialize the proctoring session"""
    try:
        self.session = get_session_by_token(self.session_token)
        if not self.session:
            logger.error(f"Session not found for token: {self.session_token}")
            return False

        if self.session.status != 'created':
            logger.error(f"Session {self.session.id} is not in 'created' status")
            return False

        # Start the session
        db = get_db_session()
        try:
            self.session.start_session()
            db.merge(self.session)
            db.commit()
            logger.info(f"Enhanced session {self.session.id} started successfully")
            return True
        finally:
            db.close()

    except Exception as e:
        logger.error(f"Error initializing enhanced session: {e}")
        return False

def initialize_camera(self):
    """Initialize camera with enhanced settings"""
    try:
        self.camera = cv2.VideoCapture(0)
        if not self.camera.isOpened():
            logger.error("Failed to open camera")
            return False

        # Enhanced camera settings
        self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
        self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
        self.camera.set(cv2.CAP_PROP_FPS, 30)
        self.camera.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0.25)

        logger.info("Enhanced camera initialized successfully")
        return True

    except Exception as e:
        logger.error(f"Error initializing enhanced camera: {e}")
        return False

def start_monitoring(self):
    """Start enhanced background monitoring"""
    if not self.initialize_session():
        return False

    if not self.initialize_camera():
        return False

    # Perform pre-test system check
    system_checker = SystemChecker()
    check_results = system_checker.perform_system_check(self.camera)

    logger.info(f"System check completed. Overall score: {check_results['overall_score']:.2f}")

    # Log system check results
    try:
        log_monitoring_event(
            session_id=self.session.id,
            event_type='system_check',
            event_data=check_results,
            severity='info'
        )
    except Exception as e:
        logger.error(f"Error logging system check: {e}")

    self.running = True
    self.monitoring_thread = threading.Thread(target=self._enhanced_monitoring_loop, daemon=True)
    self.monitoring_thread.start()

    logger.info(f"Enhanced background monitoring started for session {self.session.id}")
    return True

def _enhanced_monitoring_loop(self):
    """Enhanced monitoring loop with MediaPipe and advanced detection"""
    last_metrics_time = time.time()
    metrics_interval = 3  # Record metrics every 3 seconds
    previous_face_count = 0

    while self.running:
        try:
            start_time = time.time()

            # Capture frame
            ret, frame = self.camera.read()
            if not ret:
                logger.warning("Failed to capture frame")
                time.sleep(1)
                continue

            # Preprocess frame
            processed_frame = self.preprocess_frame(frame)

            # Process with MediaPipe
            self._process_frame_enhanced(processed_frame)

            # Check for new faces (potential violation)
            current_face_count = self.face_tracker.get_active_face_count()
            if self.face_tracker.is_new_face_detected(previous_face_count):
                if self.violation_tracker.should_log_violation('new_face_detected', time.time()):
                    self._log_enhanced_violation('new_face_detected', {
                        'previous_count': previous_face_count,
                        'current_count': current_face_count
                    })
            previous_face_count = current_face_count

            # Record metrics periodically
            current_time = time.time()
            if current_time - last_metrics_time >= metrics_interval:
                self._record_enhanced_metrics()
                last_metrics_time = current_time

            # Track processing performance
            processing_time = time.time() - start_time
            self.processing_times.append(processing_time)
            self.frame_count += 1

            # Adaptive delay based on processing time
            target_fps = 15
            target_frame_time = 1.0 / target_fps
            sleep_time = max(0, target_frame_time - processing_time)
            time.sleep(sleep_time)

        except Exception as e:
            logger.error(f"Error in enhanced monitoring loop: {e}")
            time.sleep(1)

def _process_frame_enhanced(self, frame):
    """Enhanced frame processing with MediaPipe Face Mesh"""
    if self.face_mesh is None:
        return

    try:
        # Convert to RGB for MediaPipe
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

        # Process with MediaPipe Face Mesh
        results = self.face_mesh.process(rgb_frame)

        current_time = time.time()

        # Update face tracking
        face_landmarks = results.multi_face_landmarks if results.multi_face_landmarks else []
        tracked_faces = self.face_tracker.update_faces(face_landmarks, current_time)

        # Update metrics
        self.metrics['face_count'] = len(face_landmarks)
        self.metrics['tracked_faces'] = len(tracked_faces)
        self.metrics['face_detected'] = len(face_landmarks) > 0

        # Process each detected face
        total_attention = 0
        total_liveness = 0

        for face_id, face_data in tracked_faces.items():
            # Gaze tracking
            gaze_data = self.gaze_tracker.estimate_gaze(face_data['landmarks'])

            # Update attention score
            total_attention += 1.0 if not gaze_data['looking_away'] else 0.0

            # Liveness score from blink detection
            blink_data = face_data['blink_data']
            liveness_score = min(blink_data['blink_rate'] / 15.0, 1.0)  # Normal blink rate ~15/min
            total_liveness += liveness_score

        # Calculate average scores
        num_faces = len(tracked_faces)
        if num_faces > 0:
            self.metrics['attention_score'] = total_attention / num_faces
            self.metrics['liveness_score'] = total_liveness / num_faces
            self.metrics['looking_away'] = self.metrics['attention_score'] < 0.5

            # Get blink rate from primary face (first tracked face)
            if tracked_faces:
                primary_face = list(tracked_faces.values())[0]
                self.metrics['blink_rate'] = primary_face['blink_data']['blink_rate']
        else:
            self.metrics['attention_score'] = 0.0
            self.metrics['liveness_score'] = 0.0
            self.metrics['looking_away'] = True
            self.metrics['blink_rate'] = 0

        # Check for violations
        self._check_enhanced_violations(current_time)

    except Exception as e:
        logger.error(f"Error in enhanced frame processing: {e}")

def _check_enhanced_violations(self, current_time):
    """Check for violations with enhanced logic"""
    try:
        # Face lost violation (with grace period)
        if not self.metrics['face_detected']:
            if self.violation_tracker.should_log_violation('face_lost', current_time):
                self._log_enhanced_violation('face_lost', {
                    'duration': current_time - getattr(self, 'last_face_time', current_time)
                })
        else:
            self.last_face_time = current_time

        # Multiple faces violation (with tracking context)
        if self.metrics['face_count'] > 1:
            if self.violation_tracker.should_log_violation('multiple_faces', current_time):
                self._log_enhanced_violation('multiple_faces', {
                    'face_count': self.metrics['face_count'],
                    'tracked_faces': self.metrics['tracked_faces']
                })

        # Looking away violation (with gaze tracking)
        if self.metrics['looking_away'] and self.metrics['face_detected']:
            if self.violation_tracker.should_log_violation('looking_away', current_time):
                self._log_enhanced_violation('looking_away', {
                    'attention_score': self.metrics['attention_score']
                })

        # Low liveness score (potential photo/video)
        if self.metrics['liveness_score'] < 0.3 and self.metrics['face_detected']:
            if self.violation_tracker.should_log_violation('low_liveness', current_time):
                self._log_enhanced_violation('low_liveness', {
                    'liveness_score': self.metrics['liveness_score'],
                    'blink_rate': self.metrics['blink_rate']
                })

    except Exception as e:
        logger.error(f"Error checking enhanced violations: {e}")

def _log_enhanced_violation(self, violation_type, data=None):
    """Log violation with enhanced context"""
    try:
        # Determine severity based on violation type and context
        severity_map = {
            'face_lost': 'medium',
            'multiple_faces': 'high',
            'new_face_detected': 'high',
            'looking_away': 'low',
            'low_liveness': 'high'
        }

        severity = severity_map.get(violation_type, 'medium')

        # Add risk score to violation data
        enhanced_data = data or {}
        enhanced_data['risk_score'] = self.violation_tracker.get_risk_score()
        enhanced_data['frame_count'] = self.frame_count

        # Log to violation tracker
        violation = self.violation_tracker.log_violation(violation_type, severity, enhanced_data)

        # Log to database
        log_monitoring_event(
            session_id=self.session.id,
            event_type=violation_type,
            event_data=enhanced_data,
            severity=severity
        )

        logger.warning(f"Enhanced violation logged: {violation_type} (Severity: {severity}, Risk: {enhanced_data['risk_score']:.2f})")

    except Exception as e:
        logger.error(f"Error logging enhanced violation: {e}")

def _record_enhanced_metrics(self):
    """Record enhanced metrics to database"""
    try:
        # Add performance metrics
        enhanced_metrics = self.metrics.copy()
        enhanced_metrics.update({
            'processing_fps': len(self.processing_times) / sum(self.processing_times) if self.processing_times else 0,
            'avg_processing_time': np.mean(self.processing_times) if self.processing_times else 0,
            'risk_score': self.violation_tracker.get_risk_score(),
            'frame_count': self.frame_count
        })

        record_session_metrics(
            session_id=self.session.id,
            metrics_data=enhanced_metrics
        )

    except Exception as e:
        logger.error(f"Error recording enhanced metrics: {e}")

# Add these methods to the EnhancedBackgroundProctor class
EnhancedBackgroundProctor.initialize_session = initialize_session
EnhancedBackgroundProctor.initialize_camera = initialize_camera
EnhancedBackgroundProctor.start_monitoring = start_monitoring
EnhancedBackgroundProctor._enhanced_monitoring_loop = _enhanced_monitoring_loop
EnhancedBackgroundProctor._process_frame_enhanced = _process_frame_enhanced
EnhancedBackgroundProctor._check_enhanced_violations = _check_enhanced_violations
EnhancedBackgroundProctor._log_enhanced_violation = _log_enhanced_violation
EnhancedBackgroundProctor._record_enhanced_metrics = _record_enhanced_metrics


def stop_monitoring(self):
    """Stop enhanced background monitoring"""
    self.running = False

    if self.monitoring_thread:
        self.monitoring_thread.join(timeout=5)

    if self.camera:
        self.camera.release()

    # Complete the session with enhanced metrics
    if self.session:
        try:
            db = get_db_session()
            try:
                self.session.complete_session()
                self.session.total_violations = len(self.violation_tracker.violation_history)
                self.session.attention_score = self.metrics['attention_score']

                # Add enhanced session summary
                session_summary = {
                    'total_frames': self.frame_count,
                    'avg_processing_time': np.mean(self.processing_times) if self.processing_times else 0,
                    'final_risk_score': self.violation_tracker.get_risk_score(),
                    'liveness_score': self.metrics['liveness_score'],
                    'attention_score': self.metrics['attention_score']
                }

                # Log session completion
                log_monitoring_event(
                    session_id=self.session.id,
                    event_type='session_completed',
                    event_data=session_summary,
                    severity='info'
                )

                db.merge(self.session)
                db.commit()
                logger.info(f"Enhanced session {self.session.id} completed successfully")
            finally:
                db.close()
        except Exception as e:
            logger.error(f"Error completing enhanced session: {e}")

    logger.info("Enhanced background monitoring stopped")

def log_browser_event(self, event_type, data=None):
    """Log browser-related events with enhanced context"""
    if not self.running:
        return

    try:
        current_time = time.time()

        # Check if violation should be logged based on context
        if self.violation_tracker.should_log_violation(event_type, current_time):
            if event_type == 'tab_switch':
                self.metrics['tab_switches'] += 1
                self._log_enhanced_violation('tab_switch', data)
            elif event_type == 'fullscreen_exit':
                self.metrics['fullscreen_exits'] += 1
                self._log_enhanced_violation('fullscreen_exit', data)
        else:
            logger.info(f"Browser event {event_type} not logged due to grace period")

    except Exception as e:
        logger.error(f"Error logging enhanced browser event: {e}")

def get_session_status(self):
    """Get current session status with enhanced metrics"""
    if not self.session:
        return None

    return {
        'session_id': self.session.id,
        'status': self.session.status,
        'running': self.running,
        'metrics': self.metrics.copy(),
        'risk_score': self.violation_tracker.get_risk_score(),
        'violation_count': len(self.violation_tracker.violation_history),
        'frame_count': self.frame_count,
        'performance': {
            'avg_processing_time': np.mean(self.processing_times) if self.processing_times else 0,
            'fps': len(self.processing_times) / sum(self.processing_times) if self.processing_times else 0
        }
    }

# Add these methods to the EnhancedBackgroundProctor class
EnhancedBackgroundProctor.stop_monitoring = stop_monitoring
EnhancedBackgroundProctor.log_browser_event = log_browser_event
EnhancedBackgroundProctor.get_session_status = get_session_status


class EnhancedBackgroundProctorManager:
    """Enhanced manager for multiple background proctoring sessions"""

    def __init__(self):
        self.active_proctors = {}
        self.lock = threading.Lock()
        self.system_checker = SystemChecker()

    def start_session(self, session_token):
        """Start a new enhanced background proctoring session"""
        with self.lock:
            if session_token in self.active_proctors:
                logger.warning(f"Enhanced session {session_token} already active")
                return False

            proctor = EnhancedBackgroundProctor(session_token)
            if proctor.start_monitoring():
                self.active_proctors[session_token] = proctor
                logger.info(f"Started enhanced background proctoring for session {session_token}")
                return True
            else:
                logger.error(f"Failed to start enhanced proctoring for session {session_token}")
                return False

    def stop_session(self, session_token):
        """Stop an enhanced background proctoring session"""
        with self.lock:
            if session_token in self.active_proctors:
                proctor = self.active_proctors[session_token]
                proctor.stop_monitoring()
                del self.active_proctors[session_token]
                logger.info(f"Stopped enhanced background proctoring for session {session_token}")
                return True
            else:
                logger.warning(f"Enhanced session {session_token} not found")
                return False

    def log_browser_event(self, session_token, event_type, data=None):
        """Log browser event for specific session"""
        with self.lock:
            if session_token in self.active_proctors:
                self.active_proctors[session_token].log_browser_event(event_type, data)
                return True
            else:
                logger.warning(f"Cannot log browser event: Enhanced session {session_token} not found")
                return False

    def get_session_status(self, session_token):
        """Get status of specific session"""
        with self.lock:
            if session_token in self.active_proctors:
                return self.active_proctors[session_token].get_session_status()
            else:
                return None

    def get_all_sessions_status(self):
        """Get status of all active sessions"""
        with self.lock:
            return {
                token: proctor.get_session_status()
                for token, proctor in self.active_proctors.items()
            }

    def perform_system_check(self):
        """Perform system check without starting a session"""
        try:
            camera = cv2.VideoCapture(0)
            if not camera.isOpened():
                return {'overall_score': 0.0, 'message': 'Camera not accessible'}

            results = self.system_checker.perform_system_check(camera)
            camera.release()
            return results

        except Exception as e:
            logger.error(f"System check failed: {e}")
            return {'overall_score': 0.0, 'message': f'System check failed: {e}'}

    def stop_all_sessions(self):
        """Stop all active enhanced proctoring sessions"""
        with self.lock:
            sessions_to_stop = list(self.active_proctors.keys())
            for session_token in sessions_to_stop:
                self.stop_session(session_token)
            logger.info("All enhanced background proctoring sessions stopped")


# Global enhanced manager instance
enhanced_proctor_manager = EnhancedBackgroundProctorManager()


# Convenience functions for external use
def start_enhanced_proctoring_session(session_token):
    """Start enhanced proctoring session"""
    return enhanced_proctor_manager.start_session(session_token)

def stop_enhanced_proctoring_session(session_token):
    """Stop enhanced proctoring session"""
    return enhanced_proctor_manager.stop_session(session_token)

def log_enhanced_browser_event(session_token, event_type, data=None):
    """Log browser event for enhanced session"""
    return enhanced_proctor_manager.log_browser_event(session_token, event_type, data)

def get_enhanced_session_status(session_token):
    """Get enhanced session status"""
    return enhanced_proctor_manager.get_session_status(session_token)

def perform_enhanced_system_check():
    """Perform enhanced system check"""
    return enhanced_proctor_manager.perform_system_check()


if __name__ == "__main__":
    # Test the enhanced system
    logger.info("Testing Enhanced Background Proctor System")

    # Perform system check
    check_results = perform_enhanced_system_check()
    logger.info(f"System check results: {check_results}")

    # Example usage
    test_token = "test_session_123"

    if start_enhanced_proctoring_session(test_token):
        logger.info("Enhanced proctoring session started successfully")

        # Simulate running for a short time
        time.sleep(10)

        # Get status
        status = get_enhanced_session_status(test_token)
        logger.info(f"Session status: {status}")

        # Stop session
        stop_enhanced_proctoring_session(test_token)
        logger.info("Enhanced proctoring session stopped")
    else:
        logger.error("Failed to start enhanced proctoring session")
