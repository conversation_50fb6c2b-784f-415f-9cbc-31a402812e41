def get_timer_state(timer_prefix, duration_minutes):
    """Helper to initialize timer session state and compute remaining seconds."""
    # timer_prefix: '': legacy, '_2': new
    key_start = f'test_start_time{timer_prefix}'
    key_duration = f'test_duration_seconds{timer_prefix}'
    key_last_update = f'last_timer_update{timer_prefix}'

    # Initialize session state
    if key_start not in st.session_state:
        st.session_state[key_start] = datetime.now(timezone.utc)
        st.session_state[key_duration] = duration_minutes * 60

    # Compute remaining seconds
    elapsed = (datetime.now(timezone.utc) - st.session_state[key_start]).total_seconds()
    remaining_secs = max(0, st.session_state[key_duration] - elapsed)

    # Use neutral color scheme - no more yellow/orange warnings
    color = "#667eea"  # Consistent blue color

    # Status based on time remaining
    if remaining_secs <= 60:
        status = "⏰ FINAL MINUTE"
    elif remaining_secs <= 300:
        status = "⏰ 5 MINUTES LEFT"
    elif remaining_secs <= 600:
        status = "⏰ 10 MINUTES LEFT"
    else:
        status = "⏰ TIME REMAINING"

    # Auto-refresh logic - refresh every 5 seconds for better responsiveness
    import time
    current_time = time.time()
    if key_last_update not in st.session_state:
        st.session_state[key_last_update] = current_time
    if current_time - st.session_state[key_last_update] >= 5:
        st.session_state[key_last_update] = current_time
        st.rerun()

    return remaining_secs, color, status
"""
Student Test Interface
Simple interface for students to take tests via links
"""
import streamlit as st
import json
import time
from datetime import datetime, timezone
from database_models import (
    get_db_session, ProctorSession, Test, User, MonitoringEvent,
    TestAssignment, TestResult, get_student_assignments, start_test_attempt, submit_test_result,
    create_proctor_session, log_monitoring_event
)

def show_proctoring_interface():
    """Show enhanced proctoring interface with camera preview and controls"""
    st.markdown("""
    <div class="proctoring-header">
        � LIVE PROCTORING ACTIVE - Your test session is being monitored
    </div>
    """, unsafe_allow_html=True)

    # Camera preview and controls
    col1, col2, col3 = st.columns([2, 1, 1])

    with col1:
        st.markdown("""
        <div style="background: #f8f9fa; padding: 1rem; border-radius: 10px; border-left: 4px solid #28a745;">
            <strong>📹 Camera Status:</strong> <span class="camera-status">Initializing...</span><br>
            <strong>🖥️ Screen:</strong> <span class="fullscreen-status">Fullscreen Required</span><br>
            <strong>� Monitoring:</strong> Active
        </div>
        """, unsafe_allow_html=True)

    with col2:
        if 'violation_count' not in st.session_state:
            st.session_state.violation_count = 0

        st.metric("Violations", st.session_state.violation_count, delta=None)

    with col3:
        # Session timer (if available)
        if 'test_start_time' in st.session_state:
            import time
            elapsed = int(time.time() - st.session_state.test_start_time)
            minutes = elapsed // 60
            seconds = elapsed % 60
            st.metric("Elapsed", f"{minutes:02d}:{seconds:02d}")

    # Add camera preview placeholder and proctoring controls
    st.markdown("""
    <div class="camera-preview" style="
        width: 200px;
        height: 150px;
        background: #000;
        border: 2px solid #667eea;
        border-radius: 10px;
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 12px;
    ">
        📹 Initializing Camera...
    </div>

    <div id="proctoring-controls" style="
        position: fixed;
        top: 180px;
        right: 20px;
        z-index: 1000;
        background: rgba(255,255,255,0.95);
        padding: 10px;
        border-radius: 10px;
        border: 1px solid #ddd;
        font-size: 11px;
        width: 200px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    ">
        <div style="color: #28a745; font-weight: bold;">✅ Proctoring Active</div>
        <div class="camera-status" style="color: #666;">� Camera: Initializing...</div>
        <div class="fullscreen-status" style="color: #666;">� Fullscreen: Required</div>
        <div style="color: #666;">👁️ Behavior Monitoring</div>
        <div id="violation-display" style="color: #28a745; font-weight: bold; margin-top: 5px;">
            Violations: 0
        </div>
    </div>
    """, unsafe_allow_html=True)

    # Add proctoring instructions
    st.info("""
    **🔒 Proctoring Guidelines:**
    - Fullscreen mode will be automatically enabled and maintained
    - Keep your face visible to the camera at all times
    - Do not switch tabs or open other applications
    - Ensure you are alone in the room during the test
    - Any violations will be automatically detected and logged
    - Camera access is required for test monitoring
    """)

    st.divider()

def show_simple_proctoring_interface():
    """Show simple proctoring interface with camera on top right"""
    # Camera preview on top right during test - moved from left to right
    st.markdown("""
    <div id="proctoring-camera" style="
        width: 200px;
        height: 150px;
        background: #000;
        border: 2px solid #28a745;
        border-radius: 10px;
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 12px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    ">
        📹 Initializing Camera...
    </div>

    <div id="proctoring-status" style="
        position: fixed;
        top: 180px;
        right: 20px;
        z-index: 1000;
        background: rgba(40, 167, 69, 0.9);
        color: white;
        padding: 8px 12px;
        border-radius: 20px;
        font-size: 11px;
        font-weight: bold;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    ">
        🔴 LIVE PROCTORING
    </div>

    <div id="violation-counter" style="
        position: fixed;
        top: 210px;
        right: 20px;
        z-index: 1000;
        background: rgba(255,255,255,0.95);
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 10px;
        border: 1px solid #ddd;
        box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    ">
        <span id="violation-count">Violations: 0</span>
    </div>
    """, unsafe_allow_html=True)

def create_proctoring_session(test_id, student_id):
    """Create and start a proctoring session"""
    try:
        # Create proctoring session
        session = create_proctor_session(test_id, student_id)

        # Start the session
        db = get_db_session()
        try:
            session_obj = db.query(ProctorSession).filter(
                ProctorSession.id == session.id
            ).first()
            if session_obj:
                session_obj.start_session()
                db.commit()

                # Store session info in streamlit session state
                st.session_state.proctoring_session_id = session.id
                st.session_state.proctoring_session_token = session.session_token

                return True, session.session_token
        finally:
            db.close()

    except Exception as e:
        st.error(f"Failed to create proctoring session: {e}")
        return False, None

def log_proctoring_violation(violation_type, violation_data=None):
    """Log a proctoring violation to the database"""
    if 'proctoring_session_id' not in st.session_state:
        return False

    try:
        log_monitoring_event(
            session_id=st.session_state.proctoring_session_id,
            event_type=violation_type,
            event_data=violation_data,
            severity='high' if violation_type in ['multiple_faces', 'tab_switch'] else 'medium'
        )
        return True
    except Exception as e:
        st.error(f"Failed to log violation: {e}")
        return False



def show_student_test_interface():
    """Show student test interface with login-based access"""

    # Custom CSS for test interface
    st.markdown("""
    <style>
    .test-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        border-radius: 15px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .question-card {
        background: #f8f9fa;
        color: #2c3e50;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
        border-left: 4px solid #667eea;
    }
    
    .timer-display {
        position: fixed;
        top: 20px;
        right: 20px;
        background: #ff6b6b;
        color: white;
        padding: 1rem;
        border-radius: 10px;
        font-weight: bold;
        z-index: 1000;
    }
    
    .progress-bar {
        background: #e9ecef;
        border-radius: 10px;
        height: 10px;
        margin: 1rem 0;
    }
    
    .progress-fill {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        height: 100%;
        border-radius: 10px;
        transition: width 0.3s ease;
    }
    
    .option-button {
        background: #f8f9fa;
        border: 2px solid #dee2e6;
        border-radius: 10px;
        padding: 1rem;
        margin: 0.5rem 0;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
        text-align: left;
        color: #2c3e50;
    }

    .option-button:hover {
        border-color: #667eea;
        background: rgba(102, 126, 234, 0.1);
    }

    .question-text {
        color: #2c3e50;
        font-size: 1.1rem;
        font-weight: 500;
        margin-bottom: 1rem;
        line-height: 1.6;
    }

    .question-number {
        color: #667eea;
        font-weight: 600;
        font-size: 1.2rem;
    }
    
    .option-button.selected {
        border-color: #667eea;
        background: rgba(102, 126, 234, 0.2);
    }
    
    .submit-button {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        border: none;
        border-radius: 10px;
        padding: 1rem 2rem;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
    }
    
    .submit-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(40, 167, 69, 0.3);
    }

    /* Streamlit component styling */
    .stRadio > div {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 10px;
        border: 1px solid #dee2e6;
    }

    .stRadio label {
        color: #2c3e50 !important;
        font-weight: 500;
    }

    .stRadio > div > label > div {
        color: #2c3e50 !important;
    }

    .stRadio > div > label > div > div {
        color: #2c3e50 !important;
    }

    .stRadio > div > label > div > div > p {
        color: #2c3e50 !important;
        font-size: 1rem !important;
    }

    .stRadio div[role="radiogroup"] label {
        color: #2c3e50 !important;
        background: #ffffff !important;
        padding: 0.5rem !important;
        margin: 0.25rem 0 !important;
        border-radius: 8px !important;
        border: 1px solid #dee2e6 !important;
    }

    .stRadio div[role="radiogroup"] label:hover {
        background: #e9ecef !important;
        border-color: #667eea !important;
    }

    .stTextArea > div > div > textarea {
        background: #f8f9fa !important;
        color: #2c3e50 !important;
        border: 2px solid #dee2e6 !important;
        border-radius: 10px !important;
    }

    .stMarkdown h3 {
        color: #FFA500 !important;
    }

    .stMarkdown p {
        color: #2c3e50 !important;
    }

    /* Proctoring Interface Styles */
    .proctoring-header {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        color: white;
        padding: 1rem;
        border-radius: 10px;
        text-align: center;
        margin-bottom: 1rem;
        position: sticky;
        top: 0;
        z-index: 1000;
    }

    .proctoring-status {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 10px;
        border-left: 4px solid #28a745;
        margin: 1rem 0;
    }

    .camera-preview {
        position: fixed;
        top: 20px;
        right: 20px;
        width: 200px;
        height: 150px;
        border: 2px solid #667eea;
        border-radius: 10px;
        background: #000;
        z-index: 1000;
    }

    .violation-counter {
        position: fixed;
        top: 20px;
        left: 20px;
        background: #ff6b6b;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: bold;
        z-index: 1000;
    }

    .proctoring-checklist {
        background: #e3f2fd;
        border: 1px solid #2196f3;
        border-radius: 10px;
        padding: 1.5rem;
        margin: 1rem 0;
    }

    .checklist-item {
        display: flex;
        align-items: center;
        margin: 0.5rem 0;
        padding: 0.5rem;
        border-radius: 5px;
        transition: background 0.3s ease;
    }

    .checklist-item.passed {
        background: #d4edda;
        color: #155724;
    }

    .checklist-item.failed {
        background: #f8d7da;
        color: #721c24;
    }
    </style>
    """, unsafe_allow_html=True)

    # Add proctoring JavaScript
    st.markdown("""
    <script>
    // Simple Proctoring JavaScript for test sessions
    class SimpleProctoringMonitor {
        constructor() {
            this.violations = [];
            this.isFullscreen = false;
            this.tabSwitchCount = 0;
            this.cameraStream = null;
            this.violationCount = 0;
            this.setupEventListeners();
            this.initializeCamera();
        }

        setupEventListeners() {
            // Tab visibility change detection
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    this.tabSwitchCount++;
                    this.logViolation('tab_switch', {
                        timestamp: new Date().toISOString(),
                        count: this.tabSwitchCount
                    });
                    console.log('⚠️ Tab switch detected:', this.tabSwitchCount);
                }
            });

            // Fullscreen monitoring
            document.addEventListener('fullscreenchange', () => {
                this.isFullscreen = !!document.fullscreenElement;
                if (this.isFullscreen) {
                    this.updateFullscreenStatus('active');
                } else {
                    this.updateFullscreenStatus('required');
                    this.logViolation('fullscreen_exit', {
                        timestamp: new Date().toISOString()
                    });
                    // Try to re-enable fullscreen after a short delay
                    setTimeout(() => {
                        this.enableFullscreen();
                    }, 2000);
                }
            });

            // Window focus/blur detection
            window.addEventListener('blur', () => {
                this.logViolation('window_blur', {
                    timestamp: new Date().toISOString()
                });
            });

            // Keyboard shortcuts prevention
            document.addEventListener('keydown', (e) => {
                // Prevent common cheating shortcuts
                if (e.ctrlKey && (e.key === 't' || e.key === 'n' || e.key === 'w')) {
                    e.preventDefault();
                    this.logViolation('blocked_shortcut', {
                        key: e.key,
                        timestamp: new Date().toISOString()
                    });
                }
            });

            // Right-click prevention
            document.addEventListener('contextmenu', (e) => {
                e.preventDefault();
                this.logViolation('right_click_attempt', {
                    timestamp: new Date().toISOString()
                });
            });
        }

        logViolation(type, data) {
            const violation = {
                type: type,
                data: data,
                timestamp: new Date().toISOString()
            };

            this.violations.push(violation);
            this.violationCount++;

            // Update violation counter in UI
            this.updateViolationCounter();

            // Send violation to Streamlit backend
            if (window.parent && window.parent.postMessage) {
                window.parent.postMessage({
                    type: 'proctoring_violation',
                    violation: violation
                }, '*');
            }

            console.log('🚨 Violation logged:', type, data);
        }

        updateViolationCounter() {
            const violationCounter = document.getElementById('violation-count');
            if (violationCounter) {
                violationCounter.textContent = `Violations: ${this.violationCount}`;

                // Change color based on violation count
                if (this.violationCount === 0) {
                    violationCounter.style.color = '#28a745';
                } else if (this.violationCount <= 3) {
                    violationCounter.style.color = '#ffc107';
                } else {
                    violationCounter.style.color = '#dc3545';
                }
            }
        }

        showViolationWarning(type) {
            const warnings = {
                'tab_switch': 'Warning: Tab switching detected!',
                'fullscreen_exit': 'Warning: Please return to fullscreen mode!',
                'window_blur': 'Warning: Please keep the test window focused!',
                'blocked_shortcut': 'Warning: Keyboard shortcuts are disabled!',
                'right_click_attempt': 'Warning: Right-click is disabled during test!'
            };

            const message = warnings[type] || 'Warning: Suspicious activity detected!';

            // Create warning popup
            const warning = document.createElement('div');
            warning.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #ff6b6b;
                color: white;
                padding: 1rem;
                border-radius: 10px;
                z-index: 10000;
                font-weight: bold;
                box-shadow: 0 4px 6px rgba(0,0,0,0.3);
            `;
            warning.textContent = message;

            document.body.appendChild(warning);

            // Remove after 5 seconds
            setTimeout(() => {
                if (warning.parentNode) {
                    warning.parentNode.removeChild(warning);
                }
            }, 5000);
        }

        async initializeCamera() {
            try {
                console.log('Initializing camera...');

                // Request camera access
                this.cameraStream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 640 },
                        height: { ideal: 480 },
                        facingMode: 'user'
                    },
                    audio: false
                });

                // Create video element for camera preview
                const videoElement = document.createElement('video');
                videoElement.srcObject = this.cameraStream;
                videoElement.autoplay = true;
                videoElement.muted = true;
                videoElement.playsInline = true;
                videoElement.style.cssText = `
                    width: 200px;
                    height: 150px;
                    border: 2px solid #28a745;
                    border-radius: 10px;
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 1000;
                    object-fit: cover;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                `;

                // Add camera controls overlay
                const controlsOverlay = document.createElement('div');
                controlsOverlay.style.cssText = `
                    position: fixed;
                    top: 175px;
                    right: 20px;
                    z-index: 1001;
                    background: rgba(0,0,0,0.7);
                    color: white;
                    padding: 2px 6px;
                    border-radius: 3px;
                    font-size: 10px;
                    font-weight: bold;
                `;
                controlsOverlay.textContent = '📹 LIVE';

                // Replace camera placeholder
                const placeholder = document.getElementById('proctoring-camera');
                if (placeholder && placeholder.parentNode) {
                    placeholder.parentNode.replaceChild(videoElement, placeholder);
                    document.body.appendChild(controlsOverlay);
                }

                console.log('✅ Camera initialized successfully');

                // Update camera status
                this.updateCameraStatus('active');

            } catch (error) {
                console.error('❌ Camera initialization failed:', error);

                // Show error in camera preview
                const placeholder = document.getElementById('proctoring-camera');
                if (placeholder) {
                    placeholder.innerHTML = '❌ Camera Access Denied<br><small>Please allow camera permissions</small>';
                    placeholder.style.color = '#dc3545';
                    placeholder.style.border = '2px solid #dc3545';
                    placeholder.style.fontSize = '11px';
                }

                this.logViolation('camera_access_denied', {
                    error: error.message,
                    timestamp: new Date().toISOString()
                });

                // Update camera status
                this.updateCameraStatus('denied');
            }
        }

        updateCameraStatus(status) {
            const cameraStatus = document.querySelector('.camera-status');
            if (cameraStatus) {
                if (status === 'active') {
                    cameraStatus.innerHTML = '📹 Camera: Active';
                    cameraStatus.style.color = '#28a745';
                } else if (status === 'denied') {
                    cameraStatus.innerHTML = '📹 Camera: Access Denied';
                    cameraStatus.style.color = '#dc3545';
                } else {
                    cameraStatus.innerHTML = '📹 Camera: Initializing...';
                    cameraStatus.style.color = '#ffc107';
                }
            }
        }



        startProctoring() {
            console.log('🚀 Starting simple proctoring...');

            // Enable fullscreen immediately
            this.enableFullscreen();

            // Periodic fullscreen check every 5 seconds
            setInterval(() => {
                if (!document.fullscreenElement) {
                    console.log('⚠️ Not in fullscreen, re-enabling...');
                    this.enableFullscreen();
                }
            }, 5000);

            console.log('✅ Simple proctoring active');
        }

        enableFullscreen() {
            if (!document.fullscreenElement) {
                if (document.documentElement.requestFullscreen) {
                    document.documentElement.requestFullscreen().then(() => {
                        console.log('Fullscreen enabled');
                        this.updateFullscreenStatus('active');
                    }).catch(err => {
                        console.error('Fullscreen request failed:', err);
                        this.updateFullscreenStatus('denied');
                    });
                } else if (document.documentElement.webkitRequestFullscreen) {
                    document.documentElement.webkitRequestFullscreen();
                } else if (document.documentElement.msRequestFullscreen) {
                    document.documentElement.msRequestFullscreen();
                }
            }
        }

        updateFullscreenStatus(status) {
            const fullscreenStatus = document.querySelector('.fullscreen-status');
            if (fullscreenStatus) {
                if (status === 'active') {
                    fullscreenStatus.innerHTML = '🔒 Fullscreen: Active';
                    fullscreenStatus.style.color = '#28a745';
                } else if (status === 'denied') {
                    fullscreenStatus.innerHTML = '🔒 Fullscreen: Denied';
                    fullscreenStatus.style.color = '#dc3545';
                } else {
                    fullscreenStatus.innerHTML = '🔒 Fullscreen: Required';
                    fullscreenStatus.style.color = '#ffc107';
                }
            }
        }

        performPeriodicChecks() {
            // Check if still in fullscreen
            if (!document.fullscreenElement) {
                this.updateFullscreenStatus('required');
                this.logViolation('fullscreen_required', {
                    timestamp: new Date().toISOString()
                });
            } else {
                this.updateFullscreenStatus('active');
            }

            // Check if camera is still active
            if (this.cameraStream && !this.cameraStream.active) {
                this.updateCameraStatus('disconnected');
                this.logViolation('camera_disconnected', {
                    timestamp: new Date().toISOString()
                });
            } else if (this.cameraStream && this.cameraStream.active) {
                this.updateCameraStatus('active');
            }
        }
    }

    // Initialize simple proctoring monitor
    if (typeof window.simpleProctoringMonitor === 'undefined') {
        window.simpleProctoringMonitor = new SimpleProctoringMonitor();

        // Start proctoring automatically
        setTimeout(() => {
            window.simpleProctoringMonitor.startProctoring();
        }, 500);
    }
    </script>
    """, unsafe_allow_html=True)

    # Check if student is logged in
    if 'student_logged_in' in st.session_state and st.session_state.student_logged_in:
        show_available_tests()
    else:
        show_student_login()

def show_student_login():
    """Show student login page"""
    st.markdown("""
    <div class="test-header">
        <h1>📝 Student Test Portal</h1>
        <p>Login with your credentials to access available tests</p>
    </div>
    """, unsafe_allow_html=True)

    col1, col2, col3 = st.columns([1, 2, 1])

    with col2:
        st.markdown("### Student Login")

        with st.form("student_login_form"):
            email = st.text_input(
                "Email Address",
                placeholder="Enter your registered email address"
            )

            password = st.text_input(
                "Password",
                type="password",
                placeholder="Enter your password"
            )

            submitted = st.form_submit_button("🚀 Login", type="primary")

            if submitted:
                if email and password:
                    # Validate student credentials
                    db = get_db_session()
                    try:
                        import hashlib
                        password_hash = hashlib.sha256(password.encode()).hexdigest()

                        # Find student
                        student = db.query(User).filter(
                            User.email == email,
                            User.password_hash == password_hash,
                            User.role == 'student',
                            User.is_active == True
                        ).first()

                        if student:
                            # Store login state
                            st.session_state.student_logged_in = True
                            st.session_state.student_id = student.id
                            st.session_state.student_email = student.email
                            st.session_state.student_name = student.name

                            # Update last login
                            student.last_login = datetime.now(timezone.utc)
                            db.commit()

                            st.success(f"✅ Welcome, {student.name}!")
                            st.rerun()
                        else:
                            st.error("❌ Invalid email or password. Please try again.")

                    except Exception as e:
                        st.error(f"❌ Login error: {str(e)}")
                    finally:
                        db.close()
                else:
                    st.error("Please enter both email and password.")

        # Instructions
        st.markdown("---")
        st.markdown("### 📋 Instructions")
        st.info("""
        **Before taking any test:**
        1. Ensure you have a stable internet connection
        2. Close all unnecessary applications and browser tabs
        3. Make sure your camera is working and properly positioned
        4. Find a quiet, well-lit environment
        5. Have your ID ready if requested

        **During the test:**
        - Do not switch tabs or applications
        - Keep your face visible to the camera
        - Do not use external resources unless permitted
        - Contact your instructor if you experience technical issues

        **Default Student Credentials:**
        - Email: <EMAIL>
        - Password: student123
        """)


def show_available_tests():
    """Show assigned tests for logged-in student"""
    st.markdown(f"""
    <div class="test-header">
        <h1>📚 My Assigned Tests</h1>
        <p>Welcome, {st.session_state.student_name}!</p>
    </div>
    """, unsafe_allow_html=True)

    # Logout button
    col1, col2, col3 = st.columns([1, 1, 1])
    with col3:
        if st.button("🚪 Logout"):
            # Clear session state
            for key in ['student_logged_in', 'student_id', 'student_email', 'student_name', 'current_test_result_id']:
                if key in st.session_state:
                    del st.session_state[key]
            st.rerun()

    # Check if student is taking a test
    if 'current_test_result_id' in st.session_state:
        show_test_interface_new(st.session_state.current_test_result_id)
        return

    # Get student's test assignments
    student_id = st.session_state.student_id
    assignments = get_student_assignments(student_id)

    if not assignments:
        st.info("📝 No tests have been assigned to you yet. Please check back later.")
        return

    # Separate assignments by status
    available_assignments = [a for a in assignments if a.status in ['assigned', 'started']]
    completed_assignments = [a for a in assignments if a.status == 'completed']
    expired_assignments = [a for a in assignments if a.status == 'expired']

    # Show available tests
    if available_assignments:
        st.markdown("### 📋 Available Tests")

        for assignment in available_assignments:
            db = get_db_session()
            try:
                test = db.query(Test).filter(Test.id == assignment.test_id).first()
                if not test:
                    continue

                # Calculate time remaining (handle timezone-naive datetime)
                current_time = datetime.now(timezone.utc)
                assignment_deadline = assignment.deadline
                if assignment_deadline.tzinfo is None:
                    assignment_deadline = assignment_deadline.replace(tzinfo=timezone.utc)

                time_remaining = assignment_deadline - current_time
                hours_remaining = int(time_remaining.total_seconds() // 3600)
                minutes_remaining = int((time_remaining.total_seconds() % 3600) // 60)

                # Status color
                if hours_remaining < 2:
                    status_color = "🔴"
                elif hours_remaining < 24:
                    status_color = "🟡"
                else:
                    status_color = "🟢"

                with st.container():
                    st.markdown(f"""
                    <div class="question-card">
                        <h3>{status_color} {test.title}</h3>
                        <p>{test.description or 'No description available'}</p>
                        <p><strong>Duration:</strong> {test.duration_minutes} minutes</p>
                        <p><strong>Deadline:</strong> {assignment.deadline.strftime('%Y-%m-%d %H:%M')}
                           ({hours_remaining}h {minutes_remaining}m remaining)</p>
                        <p><strong>Attempts:</strong> {assignment.attempts_used}/{assignment.attempts_allowed}</p>
                        <p><strong>Status:</strong> {assignment.status.title()}</p>
                    </div>
                    """, unsafe_allow_html=True)

                    col1, col2 = st.columns([3, 1])
                    with col2:
                        # Check if can start test
                        can_start = (
                            assignment.attempts_used < assignment.attempts_allowed and
                            current_time < assignment_deadline
                        )

                        if can_start:
                            if st.button(f"Start Test", key=f"start_test_{assignment.id}", type="primary"):
                                # Create proctoring session and start test directly
                                proctoring_success, session_token = create_proctoring_session(
                                    assignment.test_id, student_id
                                )

                                if proctoring_success:
                                    # Start test attempt
                                    success, result_id = start_test_attempt(assignment.access_token, student_id)
                                    if success:
                                        st.session_state.current_test_result_id = result_id
                                        st.success("🎥 Proctoring session started successfully!")

                                        # Initialize simple proctoring immediately
                                        st.markdown("""
                                        <script>
                                        setTimeout(() => {
                                            if (window.simpleProctoringMonitor) {
                                                window.simpleProctoringMonitor.enableFullscreen();
                                                console.log('✅ Simple proctoring initialized');
                                            }
                                        }, 100);
                                        </script>
                                        """, unsafe_allow_html=True)

                                        st.rerun()
                                    else:
                                        st.error(f"Error starting test: {result_id}")
                                else:
                                    st.error("Failed to start proctoring session. Please try again.")
                        else:
                            if assignment.attempts_used >= assignment.attempts_allowed:
                                st.error("No attempts remaining")
                            elif current_time >= assignment_deadline:
                                st.error("Deadline passed")

                    st.markdown("---")
            finally:
                db.close()

    # Show completed tests
    if completed_assignments:
        st.markdown("### ✅ Completed Tests")

        db = get_db_session()
        try:
            for assignment in completed_assignments:
                test = db.query(Test).filter(Test.id == assignment.test_id).first()
                result = db.query(TestResult).filter(
                    TestResult.assignment_id == assignment.id
                ).order_by(TestResult.completed_at.desc()).first()

                if test and result:
                    with st.expander(f"📝 {test.title} - Score: {result.percentage:.1f}%"):
                        col1, col2 = st.columns(2)

                        with col1:
                            st.write(f"**Completed:** {result.completed_at.strftime('%Y-%m-%d %H:%M')}")
                            st.write(f"**Time Taken:** {result.time_taken // 60 if result.time_taken else 0} minutes")
                            st.write(f"**Auto Score:** {result.score}/{result.max_score}")

                        with col2:
                            st.write(f"**Percentage:** {result.percentage:.1f}%")
                            if result.admin_score is not None:
                                st.write(f"**Final Score:** {result.admin_score}/{result.max_score}")
                            if result.admin_remarks:
                                st.write(f"**Teacher Remarks:** {result.admin_remarks}")
        finally:
            db.close()

    # Show expired tests
    if expired_assignments:
        st.markdown("### ⏰ Expired Tests")

        db = get_db_session()
        try:
            for assignment in expired_assignments:
                test = db.query(Test).filter(Test.id == assignment.test_id).first()
                if test:
                    st.error(f"❌ {test.title} - Deadline: {assignment.deadline.strftime('%Y-%m-%d %H:%M')}")
        finally:
            db.close()

def show_test_interface(test_id):
    """Show the actual test interface"""
    db = get_db_session()
    try:
        # Get test details
        test = db.query(Test).filter(Test.id == test_id).first()
        if not test:
            st.error("❌ Test not found.")
            return

        # Parse test questions
        try:
            questions = test.get_questions()
            if not questions:
                # Fallback to direct JSON parsing if get_questions returns empty
                test_data = json.loads(test.questions_data) if test.questions_data else {}
                questions = test_data.get('questions', []) if isinstance(test_data, dict) else test_data
        except Exception as e:
            st.error(f"Error parsing questions: {e}")
            questions = []

        # Test header
        st.markdown(f"""
        <div class="test-header">
            <h1>📝 {test.title}</h1>
            <p>Student: {st.session_state.student_name} | Duration: {test.duration_minutes} minutes</p>
        </div>
        """, unsafe_allow_html=True)

        # Back button
        col1, col2, col3 = st.columns([1, 2, 1])
        with col1:
            if st.button("⬅️ Back to Tests"):
                # Clear current test
                if 'current_test_id' in st.session_state:
                    del st.session_state.current_test_id
                if 'test_answers' in st.session_state:
                    del st.session_state.test_answers
                st.rerun()

        # --- TIMER LOGIC (moved up) ---
        actual_time_remaining, timer_color, timer_status = get_timer_state('', test.duration_minutes)

        # Clean Timer display without colored background
        with col3:
            remaining_minutes = int(actual_time_remaining // 60)
            remaining_seconds = int(actual_time_remaining % 60)

            st.markdown(f"""
            <div style="background: #f8f9fa; color: #2c3e50; padding: 1rem; border-radius: 10px; text-align: center; border: 2px solid #dee2e6; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <strong>⏰ Time Remaining</strong><br>
                <span style="font-size: 1.8rem; font-weight: bold; font-family: monospace; color: #667eea;">{remaining_minutes:02d}:{remaining_seconds:02d}</span><br>
                <small style="font-size: 0.8rem; color: #6c757d;">{timer_status}</small>
            </div>
            """, unsafe_allow_html=True)

            # Only show critical warning when time is almost up
            if actual_time_remaining <= 10:
                st.error("⚠️ Less than 10 seconds remaining!")

            # Auto-submit when time is up
            if actual_time_remaining <= 0:
                st.error("⏰ Time is up! Auto-submitting test...")
                submit_test_simple(test, questions)
                return

        if not questions:
            st.error("❌ No questions found for this test.")
            return

        # Initialize answers in session state
        if 'test_answers' not in st.session_state:
            st.session_state.test_answers = {}

        # ...existing code...

        # Progress bar
        total_questions = len(questions)
        answered_questions = len(st.session_state.test_answers)
        progress = (answered_questions / total_questions) * 100 if total_questions > 0 else 0

        st.markdown(f"""
        <div class="progress-bar">
            <div class="progress-fill" style="width: {progress}%"></div>
        </div>
        <p style="text-align: center; margin: 0.5rem 0;">
            Progress: {answered_questions}/{total_questions} questions answered ({progress:.1f}%)
        </p>
        """, unsafe_allow_html=True)

        # Display questions
        for i, question in enumerate(questions):
            with st.container():
                st.markdown(f"""
                <div class="question-card">
                    <h3>Question {i+1} of {total_questions}</h3>
                    <p style="font-size: 1.1rem; margin: 1rem 0;">{question.get('question', 'No question text')}</p>
                </div>
                """, unsafe_allow_html=True)

                question_key = f"q_{i}"

                if question.get('type') == 'multiple_choice':
                    options = question.get('options', [])
                    selected = st.radio(
                        f"Select your answer for Question {i+1}:",
                        options,
                        key=question_key,
                        index=None
                    )

                    if selected:
                        st.session_state.test_answers[question_key] = {
                            'answer': options.index(selected),
                            'text': selected
                        }

                elif question.get('type') == 'true_false':
                    selected = st.radio(
                        f"Select your answer for Question {i+1}:",
                        ['True', 'False'],
                        key=question_key,
                        index=None
                    )

                    if selected:
                        st.session_state.test_answers[question_key] = {
                            'answer': selected == 'True',
                            'text': selected
                        }

                elif question.get('type') == 'short_answer':
                    answer = st.text_area(
                        f"Your answer for Question {i+1}:",
                        key=question_key,
                        height=100
                    )

                    if answer.strip():
                        st.session_state.test_answers[question_key] = {
                            'answer': answer.strip(),
                            'text': answer.strip()
                        }

                st.markdown("---")

        # Submit button
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            if st.button("🎯 Submit Test", type="primary", use_container_width=True):
                if len(st.session_state.test_answers) < total_questions:
                    st.info(f"📝 You have answered {len(st.session_state.test_answers)} out of {total_questions} questions.")

                    if st.button("✅ Submit Test", type="secondary"):
                        submit_test_simple(test, questions)
                else:
                    submit_test_simple(test, questions)

    except Exception as e:
        st.error(f"❌ Error loading test: {str(e)}")
    finally:
        db.close()

def show_test_interface_new(result_id):
    """Show the test interface using the new assignment system"""

    # Show simple proctoring interface (camera on top left)
    show_simple_proctoring_interface()

    # ...removed all float-based initialization of test_start_time...

    db = get_db_session()
    try:
        # Get test result and related data
        result = db.query(TestResult).filter(TestResult.id == result_id).first()
        if not result:
            st.error("❌ Test session not found.")
            return

        test = db.query(Test).filter(Test.id == result.test_id).first()
        if not test:
            st.error("❌ Test not found.")
            return

        assignment = db.query(TestAssignment).filter(TestAssignment.id == result.assignment_id).first()
        if not assignment:
            st.error("❌ Assignment not found.")
            return

        # Check if test is still valid
        current_time = datetime.now(timezone.utc)
        assignment_deadline = assignment.deadline
        if assignment_deadline.tzinfo is None:
            assignment_deadline = assignment_deadline.replace(tzinfo=timezone.utc)

        if assignment_deadline < current_time:
            st.error("❌ Test deadline has passed.")
            return

        # Parse test questions
        try:
            questions = test.get_questions()
        except:
            questions = []

        # Test header
        st.markdown(f"""
        <div class="test-header">
            <h1>📝 {test.title}</h1>
            <p>Student: {st.session_state.student_name} | Duration: {test.duration_minutes} minutes</p>
        </div>
        """, unsafe_allow_html=True)

        # Back button and timer
        col1, col2, col3 = st.columns([1, 2, 1])
        with col1:
            if st.button("⬅️ Back to Tests"):
                # Clear current test
                if 'current_test_result_id' in st.session_state:
                    del st.session_state.current_test_result_id
                if 'test_answers' in st.session_state:
                    del st.session_state.test_answers
                st.rerun()

        # --- TIMER LOGIC (moved up, always datetime) ---
        actual_time_remaining_2, timer_color_2, timer_status_2 = get_timer_state('_2', test.duration_minutes)

        with col3:
            remaining_minutes_2 = int(actual_time_remaining_2 // 60)
            remaining_seconds_2 = int(actual_time_remaining_2 % 60)

            st.markdown(f"""
            <div style="background: #f8f9fa; color: #2c3e50; padding: 1rem; border-radius: 10px; text-align: center; border: 2px solid #dee2e6; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <strong>⏰ Test Timer</strong><br>
                <span style="font-size: 1.8rem; font-weight: bold; font-family: monospace; color: #667eea;">{remaining_minutes_2:02d}:{remaining_seconds_2:02d}</span><br>
                <small style="font-size: 0.8rem; color: #6c757d;">{timer_status_2}</small>
            </div>
            """, unsafe_allow_html=True)

            # Only show critical warning when time is almost up
            if actual_time_remaining_2 <= 10:
                st.error("⚠️ Less than 10 seconds remaining!")

            # Auto-submit when time is up
            if actual_time_remaining_2 <= 0:
                st.error("⏰ Time is up! Auto-submitting test...")
                submit_test_new(result_id)
                return

        if not questions:
            st.error("❌ No questions found for this test.")
            return

        # Initialize answers in session state
        if 'test_answers' not in st.session_state:
            st.session_state.test_answers = {}

        # ...existing code...

        # Progress bar
        total_questions = len(questions)
        answered_questions = len(st.session_state.test_answers)
        progress = (answered_questions / total_questions) * 100 if total_questions > 0 else 0

        st.markdown(f"""
        <div class="progress-bar">
            <div class="progress-fill" style="width: {progress}%"></div>
        </div>
        <p style="text-align: center; margin: 0.5rem 0;">
            Progress: {answered_questions}/{total_questions} questions answered ({progress:.1f}%)
        </p>
        """, unsafe_allow_html=True)

        # Display questions
        for i, question in enumerate(questions):
            with st.container():
                st.markdown(f"""
                <div class="question-card">
                    <h3>Question {i+1} of {total_questions}</h3>
                    <p style="font-size: 1.1rem; margin: 1rem 0;">{question.get('question', 'No question text')}</p>
                </div>
                """, unsafe_allow_html=True)

                question_key = f"q_{i}"

                if question.get('type') == 'multiple_choice':
                    options = question.get('options', [])
                    selected = st.radio(
                        f"Select your answer for Question {i+1}:",
                        options,
                        key=question_key,
                        index=None
                    )

                    if selected:
                        st.session_state.test_answers[question_key] = {
                            'answer': options.index(selected),
                            'text': selected
                        }

                elif question.get('type') == 'true_false':
                    selected = st.radio(
                        f"Select your answer for Question {i+1}:",
                        ['True', 'False'],
                        key=question_key,
                        index=None
                    )

                    if selected:
                        st.session_state.test_answers[question_key] = {
                            'answer': selected == 'True',
                            'text': selected
                        }

                elif question.get('type') == 'short_answer':
                    answer = st.text_area(
                        f"Your answer for Question {i+1}:",
                        key=question_key,
                        height=100
                    )

                    if answer.strip():
                        st.session_state.test_answers[question_key] = {
                            'answer': answer.strip(),
                            'text': answer.strip()
                        }

                st.markdown("---")

        # Submit button
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            if st.button("🎯 Submit Test", type="primary", use_container_width=True):
                if len(st.session_state.test_answers) < total_questions:
                    st.info(f"📝 You have answered {len(st.session_state.test_answers)} out of {total_questions} questions.")

                    if st.button("✅ Submit Test", type="secondary"):
                        submit_test_new(result_id)
                else:
                    submit_test_new(result_id)

    except Exception as e:
        st.error(f"❌ Error loading test: {str(e)}")
    finally:
        db.close()


def submit_test_new(result_id):
    """Submit the test using the new assignment system"""
    try:
        # Submit test result
        success, result_data = submit_test_result(result_id, st.session_state.test_answers)

        if success:
            # Show results
            st.success("🎉 Test submitted successfully!")

            score = result_data['score']
            max_score = result_data['max_score']
            percentage = result_data['percentage']
            correct_answers = result_data['correct_answers']
            total_questions = result_data['total_questions']

            st.markdown(f"""
            <div class="test-header">
                <h2>📊 Test Results</h2>
                <h3>Score: {correct_answers}/{total_questions} ({percentage:.1f}%)</h3>
                <h4>Points: {score}/{max_score}</h4>
                <p>{"🎉 Congratulations! You passed!" if percentage >= 70 else "📚 Keep studying and try again!"}</p>
            </div>
            """, unsafe_allow_html=True)

            # Clear test session
            for key in ['test_answers', 'current_test_result_id']:
                if key in st.session_state:
                    del st.session_state[key]

            if st.button("🏠 Return to Tests"):
                st.rerun()
        else:
            st.error(f"❌ Error submitting test: {result_data}")

    except Exception as e:
        st.error(f"❌ Error submitting test: {str(e)}")


def submit_test_simple(test, questions):
    """Submit the test and calculate results (legacy function)"""
    try:
        # Calculate score
        total_questions = len(questions)
        correct_answers = 0

        for i, question in enumerate(questions):
            question_key = f"q_{i}"
            if question_key in st.session_state.test_answers:
                user_answer = st.session_state.test_answers[question_key]['answer']
                correct_answer = question.get('correct_answer')

                if user_answer == correct_answer:
                    correct_answers += 1

        score_percentage = (correct_answers / total_questions) * 100 if total_questions > 0 else 0

        # Show results
        st.success("🎉 Test submitted successfully!")

        st.markdown(f"""
        <div class="test-header">
            <h2>📊 Test Results</h2>
            <h3>Score: {correct_answers}/{total_questions} ({score_percentage:.1f}%)</h3>
            <p>{"🎉 Congratulations! You passed!" if score_percentage >= 70 else "📚 Keep studying and try again!"}</p>
        </div>
        """, unsafe_allow_html=True)

        # Clear test session
        for key in ['test_answers', 'current_test_id']:
            if key in st.session_state:
                del st.session_state[key]

        if st.button("🏠 Return to Tests"):
            st.rerun()

    except Exception as e:
        st.error(f"❌ Error submitting test: {str(e)}")

def show_student_test_interface_with_proctoring():
    """Enhanced student test interface with integrated proctoring for main dashboard"""

    # Add proctoring CSS and JavaScript
    st.markdown("""
    <style>
    .proctoring-header {
        background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 1rem;
        border-radius: 10px;
        text-align: center;
        margin-bottom: 1rem;
        font-weight: bold;
    }

    .test-container {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 10px;
        border: 1px solid #dee2e6;
        margin-bottom: 1rem;
    }

    .question-container {
        background: white;
        padding: 1.5rem;
        border-radius: 8px;
        border-left: 4px solid #007bff;
        margin-bottom: 1rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .option-container {
        background: #f8f9fa;
        padding: 0.8rem;
        border-radius: 6px;
        margin: 0.5rem 0;
        border: 1px solid #e9ecef;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .option-container:hover {
        background: #e9ecef;
        border-color: #007bff;
    }
    </style>
    """, unsafe_allow_html=True)

    # Check if student is logged in (from main app session)
    if not st.session_state.get('authenticated') or st.session_state.get('user_role') != 'student':
        st.error("❌ Access denied. Please log in as a student.")
        return

    # Get student information from session
    student_id = st.session_state.get('user_id')
    student_name = st.session_state.get('user_name')

    if not student_id:
        st.error("❌ Student information not found. Please log in again.")
        return

    # Check if student is currently taking a test
    if 'current_test_result_id' in st.session_state:
        show_test_interface_new(st.session_state.current_test_result_id)
        return

    # Show student dashboard header
    st.markdown(f"""
    <div class="proctoring-header">
        📚 Welcome to Your Test Dashboard, {student_name}!
        <br><small>Integrated Proctoring System Active</small>
    </div>
    """, unsafe_allow_html=True)

    # Add JavaScript proctoring monitor
    st.markdown("""
    <script>
    // Simple Proctoring JavaScript for main dashboard
    class SimpleProctoringMonitor {
        constructor() {
            this.violations = [];
            this.isFullscreen = false;
            this.tabSwitchCount = 0;
            this.cameraStream = null;
            this.violationCount = 0;
            this.setupEventListeners();
            this.initializeCamera();
        }

        setupEventListeners() {
            // Tab switch detection
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    this.tabSwitchCount++;
                    this.logViolation('tab_switch', {
                        timestamp: new Date().toISOString(),
                        count: this.tabSwitchCount
                    });
                    console.log('⚠️ Tab switch detected:', this.tabSwitchCount);
                }
            });

            // Fullscreen monitoring
            document.addEventListener('fullscreenchange', () => {
                this.isFullscreen = !!document.fullscreenElement;
                if (!this.isFullscreen) {
                    this.logViolation('fullscreen_exit', {
                        timestamp: new Date().toISOString()
                    });
                    console.log('⚠️ Fullscreen exit detected');

                    // Auto re-enable fullscreen after 2 seconds
                    setTimeout(() => {
                        this.enableFullscreen();
                    }, 2000);
                }
            });

            // Window focus/blur detection
            window.addEventListener('blur', () => {
                this.logViolation('window_blur', {
                    timestamp: new Date().toISOString()
                });
                console.log('⚠️ Window blur detected');
            });

            // Prevent right-click
            document.addEventListener('contextmenu', (e) => {
                e.preventDefault();
                this.logViolation('right_click_attempt', {
                    timestamp: new Date().toISOString()
                });
            });

            // Block common shortcuts
            document.addEventListener('keydown', (e) => {
                if (e.key === 'F12' ||
                    (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                    (e.ctrlKey && e.key === 'u') ||
                    (e.altKey && e.key === 'Tab')) {
                    e.preventDefault();
                    this.logViolation('blocked_shortcut', {
                        key: e.key,
                        timestamp: new Date().toISOString()
                    });
                }
            });
        }

        async initializeCamera() {
            try {
                console.log('Initializing camera...');

                // Request camera access
                this.cameraStream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 200 },
                        height: { ideal: 150 },
                        facingMode: 'user'
                    },
                    audio: false
                });

                // Create video element for camera preview
                const videoElement = document.createElement('video');
                videoElement.srcObject = this.cameraStream;
                videoElement.autoplay = true;
                videoElement.muted = true;
                videoElement.playsInline = true;
                videoElement.style.cssText = `
                    width: 200px;
                    height: 150px;
                    border: 2px solid #28a745;
                    border-radius: 10px;
                    position: fixed;
                    top: 20px;
                    left: 20px;
                    z-index: 1000;
                    object-fit: cover;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                `;

                // Replace camera placeholder
                const placeholder = document.getElementById('proctoring-camera');
                if (placeholder && placeholder.parentNode) {
                    placeholder.parentNode.replaceChild(videoElement, placeholder);
                }

                console.log('✅ Camera initialized successfully');

            } catch (error) {
                console.error('❌ Camera initialization failed:', error);

                // Show error in camera preview
                const placeholder = document.getElementById('proctoring-camera');
                if (placeholder) {
                    placeholder.innerHTML = '❌ Camera Access Denied<br><small>Please allow camera permissions</small>';
                    placeholder.style.color = '#dc3545';
                    placeholder.style.border = '2px solid #dc3545';
                    placeholder.style.fontSize = '11px';
                }

                this.logViolation('camera_access_denied', {
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        }

        enableFullscreen() {
            try {
                if (document.documentElement.requestFullscreen) {
                    document.documentElement.requestFullscreen();
                } else if (document.documentElement.webkitRequestFullscreen) {
                    document.documentElement.webkitRequestFullscreen();
                } else if (document.documentElement.msRequestFullscreen) {
                    document.documentElement.msRequestFullscreen();
                }
                console.log('🖥️ Fullscreen enabled');
            } catch (error) {
                console.error('❌ Fullscreen failed:', error);
            }
        }

        logViolation(type, data) {
            const violation = {
                type: type,
                data: data,
                timestamp: new Date().toISOString()
            };

            this.violations.push(violation);
            this.violationCount++;

            // Update violation counter in UI
            this.updateViolationCounter();

            // Send violation to Streamlit backend
            if (window.parent && window.parent.postMessage) {
                window.parent.postMessage({
                    type: 'proctoring_violation',
                    violation: violation
                }, '*');
            }

            console.log('🚨 Violation logged:', type, data);
        }

        updateViolationCounter() {
            const violationCounter = document.getElementById('violation-count');
            if (violationCounter) {
                violationCounter.textContent = `Violations: ${this.violationCount}`;

                // Change color based on violation count
                if (this.violationCount === 0) {
                    violationCounter.style.color = '#28a745';
                } else if (this.violationCount <= 3) {
                    violationCounter.style.color = '#ffc107';
                } else {
                    violationCounter.style.color = '#dc3545';
                }
            }
        }

        startProctoring() {
            console.log('🚀 Starting simple proctoring...');

            // Enable fullscreen immediately
            this.enableFullscreen();

            // Periodic fullscreen check every 5 seconds
            setInterval(() => {
                if (!document.fullscreenElement) {
                    console.log('⚠️ Not in fullscreen, re-enabling...');
                    this.enableFullscreen();
                }
            }, 5000);

            console.log('✅ Simple proctoring active');
        }
    }

    // Initialize simple proctoring monitor
    if (typeof window.simpleProctoringMonitor === 'undefined') {
        window.simpleProctoringMonitor = new SimpleProctoringMonitor();

        // Start proctoring automatically
        setTimeout(() => {
            window.simpleProctoringMonitor.startProctoring();
        }, 500);
    }
    </script>
    """, unsafe_allow_html=True)

    # Show available tests
    show_available_tests_with_proctoring(student_id)

def show_available_tests_with_proctoring(student_id):
    """Show available tests with proctoring integration"""

    st.subheader("📋 Your Assigned Tests")

    # Get student assignments
    try:
        assignments = get_student_assignments(student_id)

        if not assignments:
            st.info("📝 No tests assigned to you at this time.")
            return

        # Separate active and expired assignments
        from datetime import datetime, timezone
        current_time = datetime.now(timezone.utc)

        active_assignments = []
        expired_assignments = []

        for assignment in assignments:
            if assignment.deadline and assignment.deadline < current_time:
                expired_assignments.append(assignment)
            else:
                active_assignments.append(assignment)

        # Show active assignments
        if active_assignments:
            st.markdown("### 🟢 Available Tests")

            db = get_db_session()
            try:
                for assignment in active_assignments:
                    test = db.query(Test).filter(Test.id == assignment.test_id).first()
                    if test:
                        with st.container():
                            col1, col2, col3 = st.columns([3, 1, 1])

                            with col1:
                                # Get question count properly
                                try:
                                    questions = test.get_questions()
                                    question_count = len(questions)
                                except:
                                    question_count = 0

                                st.markdown(f"""
                                <div class="test-container">
                                    <h4>📝 {test.title}</h4>
                                    <p><strong>Duration:</strong> {test.duration_minutes} minutes</p>
                                    <p><strong>Questions:</strong> {question_count}</p>
                                    <p><strong>Deadline:</strong> {assignment.deadline.strftime('%Y-%m-%d %H:%M') if assignment.deadline else 'No deadline'}</p>
                                </div>
                                """, unsafe_allow_html=True)

                            with col2:
                                st.markdown("**Proctoring:**")
                                st.success("✅ Camera Required")
                                st.success("✅ Fullscreen Mode")
                                st.success("✅ Tab Monitoring")

                            with col3:
                                # Check if student can start the test
                                can_start = True

                                if can_start:
                                    if st.button(f"🚀 Start Proctored Test", key=f"start_test_{assignment.id}", type="primary"):
                                        # Create proctoring session and start test
                                        proctoring_success, session_token = create_proctoring_session(
                                            assignment.test_id, student_id
                                        )

                                        if proctoring_success:
                                            # Start test attempt
                                            success, result_id = start_test_attempt(assignment.access_token, student_id)
                                            if success:
                                                st.session_state.current_test_result_id = result_id
                                                st.success("🎥 Proctoring session started successfully!")

                                                # Initialize proctoring immediately
                                                st.markdown("""
                                                <script>
                                                setTimeout(() => {
                                                    if (window.simpleProctoringMonitor) {
                                                        window.simpleProctoringMonitor.enableFullscreen();
                                                        console.log('✅ Proctoring initialized from main dashboard');
                                                    }
                                                }, 100);
                                                </script>
                                                """, unsafe_allow_html=True)

                                                st.rerun()
                                            else:
                                                st.error("❌ Failed to start test. Please try again.")
                                        else:
                                            st.error("❌ Failed to initialize proctoring session.")
                                else:
                                    st.info("⏳ Test not available")

                            st.divider()
            finally:
                db.close()

        # Show expired tests
        if expired_assignments:
            st.markdown("### ⏰ Expired Tests")

            db = get_db_session()
            try:
                for assignment in expired_assignments:
                    test = db.query(Test).filter(Test.id == assignment.test_id).first()
                    if test:
                        st.error(f"❌ {test.title} - Deadline: {assignment.deadline.strftime('%Y-%m-%d %H:%M')}")
            finally:
                db.close()

    except Exception as e:
        st.error(f"❌ Error loading tests: {str(e)}")

if __name__ == "__main__":
    show_student_test_interface()
